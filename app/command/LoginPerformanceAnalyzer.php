<?php
declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

/**
 * 登录性能分析器
 */
class LoginPerformanceAnalyzer extends Command
{
    protected function configure()
    {
        $this->setName('login:analyze')
            ->setDescription('分析登录性能问题')
            ->addOption('test', 't', null, '运行性能测试')
            ->addOption('stats', 's', null, '显示性能统计')
            ->addOption('optimize', 'o', null, '执行性能优化')
            ->addOption('benchmark', 'b', null, '运行基准测试');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('<info>登录性能分析器</info>');
        
        if ($input->getOption('test')) {
            $this->runPerformanceTest($output);
        }
        
        if ($input->getOption('stats')) {
            $this->showPerformanceStats($output);
        }
        
        if ($input->getOption('optimize')) {
            $this->runOptimization($output);
        }
        
        if ($input->getOption('benchmark')) {
            $this->runBenchmark($output);
        }
        
        if (!$input->getOption('test') && !$input->getOption('stats') && 
            !$input->getOption('optimize') && !$input->getOption('benchmark')) {
            $this->showHelp($output);
        }
        
        return 0;
    }
    
    /**
     * 运行性能测试
     */
    private function runPerformanceTest(Output $output): void
    {
        $output->writeln('<comment>运行性能测试...</comment>');
        
        $tests = [
            '数据库连接' => function() {
                $start = microtime(true);
                Db::query('SELECT 1');
                return (microtime(true) - $start) * 1000;
            },
            '缓存操作' => function() {
                $start = microtime(true);
                Cache::set('test_key', 'test_value', 10);
                Cache::get('test_key');
                Cache::delete('test_key');
                return (microtime(true) - $start) * 1000;
            },
            '用户查询' => function() {
                $start = microtime(true);
                \app\common\model\Admin::where('username', 'admin')->find();
                return (microtime(true) - $start) * 1000;
            },
            '密码哈希' => function() {
                $start = microtime(true);
                md5(md5('test_password') . 'test_salt');
                return (microtime(true) - $start) * 1000;
            },
            'OAuth2Server初始化' => function() {
                $start = microtime(true);
                new \app\common\service\OAuth2Server();
                return (microtime(true) - $start) * 1000;
            },
            '配置读取' => function() {
                $start = microtime(true);
                config('oauth.clients.admin');
                return (microtime(true) - $start) * 1000;
            },
            '角色查询' => function() {
                $start = microtime(true);
                \app\common\model\Role::find(1);
                return (microtime(true) - $start) * 1000;
            },
            '菜单查询' => function() {
                $start = microtime(true);
                \app\common\model\Menu::where('status', 1)->limit(10)->select();
                return (microtime(true) - $start) * 1000;
            }
        ];
        
        $results = [];
        foreach ($tests as $name => $test) {
            try {
                $time = $test();
                $results[$name] = round($time, 2);
                
                $color = $time > 50 ? 'error' : ($time > 20 ? 'comment' : 'info');
                $output->writeln("  <{$color}>{$name}: {$results[$name]}ms</{$color}>");
            } catch (\Exception $e) {
                $output->writeln("  <error>{$name}: 失败 - {$e->getMessage()}</error>");
            }
        }
        
        $totalTime = array_sum($results);
        $output->writeln('');
        $output->writeln("<info>总耗时: {$totalTime}ms</info>");
        
        if ($totalTime > 200) {
            $output->writeln('<error>⚠️  总耗时超过200ms，建议进行优化</error>');
        } else {
            $output->writeln('<info>✓ 性能表现良好</info>');
        }
    }
    
    /**
     * 显示性能统计
     */
    private function showPerformanceStats(Output $output): void
    {
        $output->writeln('<comment>登录性能统计:</comment>');
        
        $today = date('Y-m-d');
        $statsKey = "login_performance_stats:{$today}";
        $stats = Cache::get($statsKey);
        
        if (!$stats) {
            $output->writeln('<info>今日暂无登录性能数据</info>');
            return;
        }
        
        $output->writeln("日期: {$today}");
        $output->writeln("总请求数: {$stats['total_requests']}");
        $output->writeln("平均响应时间: {$stats['avg_time']}ms");
        $output->writeln("最大响应时间: {$stats['max_time']}ms");
        $output->writeln("最小响应时间: {$stats['min_time']}ms");
        $output->writeln("慢请求数: {$stats['slow_requests']}");
        
        $slowRate = $stats['total_requests'] > 0 ? 
            round(($stats['slow_requests'] / $stats['total_requests']) * 100, 2) : 0;
        $output->writeln("慢请求率: {$slowRate}%");
        
        if ($stats['avg_time'] > 200) {
            $output->writeln('<error>⚠️  平均响应时间过长，需要优化</error>');
        } elseif ($stats['avg_time'] > 100) {
            $output->writeln('<comment>⚠️  响应时间偏长，建议优化</comment>');
        } else {
            $output->writeln('<info>✓ 响应时间正常</info>');
        }
    }
    
    /**
     * 运行优化
     */
    private function runOptimization(Output $output): void
    {
        $output->writeln('<comment>执行性能优化...</comment>');
        
        try {
            // 1. 预热缓存
            $output->writeln('1. 预热缓存...');
            $this->warmupCache();
            $output->writeln('   <info>✓ 缓存预热完成</info>');
            
            // 2. 优化数据库连接
            $output->writeln('2. 优化数据库连接...');
            Db::connect();
            $output->writeln('   <info>✓ 数据库连接优化完成</info>');
            
            // 3. 清理过期缓存
            $output->writeln('3. 清理过期缓存...');
            Cache::clear();
            $output->writeln('   <info>✓ 缓存清理完成</info>');
            
            $output->writeln('<info>性能优化完成！</info>');
        } catch (\Exception $e) {
            $output->writeln("<error>优化失败: {$e->getMessage()}</error>");
        }
    }
    
    /**
     * 运行基准测试
     */
    private function runBenchmark(Output $output): void
    {
        $output->writeln('<comment>运行登录基准测试...</comment>');
        
        $iterations = 10;
        $times = [];
        
        for ($i = 1; $i <= $iterations; $i++) {
            $start = microtime(true);
            
            try {
                // 模拟登录流程的关键步骤
                \app\common\model\Admin::where('username', 'admin')->find();
                md5(md5('test_password') . 'test_salt');
                new \app\common\service\OAuth2Server();
                
                $time = (microtime(true) - $start) * 1000;
                $times[] = $time;
                
                $output->writeln("  第{$i}次: " . round($time, 2) . "ms");
            } catch (\Exception $e) {
                $output->writeln("  第{$i}次: 失败 - {$e->getMessage()}");
            }
        }
        
        if (!empty($times)) {
            $avgTime = array_sum($times) / count($times);
            $maxTime = max($times);
            $minTime = min($times);
            
            $output->writeln('');
            $output->writeln("<info>基准测试结果:</info>");
            $output->writeln("平均时间: " . round($avgTime, 2) . "ms");
            $output->writeln("最大时间: " . round($maxTime, 2) . "ms");
            $output->writeln("最小时间: " . round($minTime, 2) . "ms");
            
            if ($avgTime > 100) {
                $output->writeln('<error>⚠️  性能需要优化</error>');
            } else {
                $output->writeln('<info>✓ 性能表现良好</info>');
            }
        }
    }
    
    /**
     * 预热缓存
     */
    private function warmupCache(): void
    {
        // 预热常用配置
        config('oauth.clients.admin');
        config('database');
        
        // 预热常用查询
        \app\common\model\Menu::where('status', 1)->limit(1)->find();
        \app\common\model\Role::limit(1)->find();
    }
    
    /**
     * 显示帮助
     */
    private function showHelp(Output $output): void
    {
        $output->writeln('<comment>使用方法:</comment>');
        $output->writeln('  php think login:analyze --test      运行性能测试');
        $output->writeln('  php think login:analyze --stats     显示性能统计');
        $output->writeln('  php think login:analyze --optimize  执行性能优化');
        $output->writeln('  php think login:analyze --benchmark 运行基准测试');
        $output->writeln('');
        $output->writeln('<comment>示例:</comment>');
        $output->writeln('  php think login:analyze --test --optimize');
    }
}
