<?php

namespace app\telegram\service;

use app\common\service\TelegramBotService;
use app\common\model\DaifuOrder;
use app\common\model\Settlement;
use app\common\model\BotAdmin;
use app\common\model\BotQuery;
use app\common\model\BotBind;
use app\telegram\service\BaseCommandService;
use app\telegram\service\NotificationService;
use app\telegram\service\OrderForwardService;
use app\telegram\service\BotConfigHelper;
use think\facade\Db;
use TelegramBot\Api\Types\CallbackQuery;

/**
 * Telegram 回调处理器
 */
class CallbackHandler extends BaseCommandService
{
    protected $telegramService;
    protected $currentBotId = 0;
    
    public function __construct()
    {
        $this->telegramService = new TelegramBotService();
    }

    /**
     * 设置当前机器人ID
     */
    public function setCurrentBotId(int $botId): void
    {
        $this->currentBotId = $botId;
    }

    /**
     * 处理回调查询（通用方法）
     */
    public function handle(string $callbackId, string $data, int $chatId, int $messageId, int $userId): void
    {
        try {
            // 记录回调数据用于调试            // 解析回调数据
            $parts = explode(':', $data, 2);
            $action = $parts[0];
            $params = isset($parts[1]) ? $parts[1] : '';            switch ($action) {
                case 'notify_all':
                    $this->handleNotifyAll($chatId, $messageId, $userId, $params);
                    break;
                case 'notify_select':
                    $this->handleNotifySelect($chatId, $messageId, $userId, $params);
                    break;
                case 'notify_cancel':
                    $this->handleNotifyCancel($chatId, $messageId, $userId);
                    break;
                case 'notify_merchant':
                    $this->handleNotifyMerchant($chatId, $messageId, $userId, $params);
                    break;
                case 'grab_order':
                    $this->handleGrabOrder($chatId, $messageId, $userId, $params);
                    break;
                case 'view_order':
                    $this->handleViewOrder($chatId, $messageId, $userId, $params);
                    break;
                case 'approve_settlement':
                    $this->handleApproveSettlement($chatId, $messageId, $userId, $params);
                    break;
                case 'reject_settlement':
                    $this->handleRejectSettlement($chatId, $messageId, $userId, $params);
                    break;
                case 'reply_query':
                    $this->handleReplyQuery($chatId, $messageId, $userId, $params);
                    break;
                case 'orders_page':
                    $this->handleOrdersPagination($chatId, $messageId, $userId, $params);
                    break;
                case 'forward_order':
                    $this->handleForwardOrder($chatId, $messageId, $userId, $params);
                    break;
                case 'query_order':
                    $this->handleQueryOrder($callbackId, $chatId, $messageId, $userId, $params);
                    break;
                case 'urge_order':
                    $this->handleUrgeOrder($callbackId, $chatId, $messageId, $userId, $params);
                    break;

                case 'confirm':
                    $this->handleConfirmAction($chatId, $messageId, $userId, $params);
                    break;
                case 'complete_order':
                    $this->handleCompleteOrder($chatId, $messageId, $userId, $params);
                    break;
                case 'fail_order':
                    $this->handleFailOrder($chatId, $messageId, $userId, $params);
                    break;
                // 新增的订单转发相关回调
                case 'contact_service':
                    $this->handleContactService($chatId, $messageId, $userId, $params);
                    break;
                case 'retry_order':
                    $this->handleRetryOrder($chatId, $messageId, $userId, $params);
                    break;
                case 'check_reason':
                    $this->handleCheckReason($chatId, $messageId, $userId, $params);
                    break;
                case 'order_detail':
                    $this->handleOrderDetail($chatId, $messageId, $userId, $params);
                    break;
                case 'order_log':
                    $this->handleOrderLog($chatId, $messageId, $userId, $params);
                    break;
                case 'confirm_process':
                    $this->handleConfirmProcess($chatId, $messageId, $userId, $params);
                    break;
                case 'reject_process':
                    $this->handleRejectProcess($chatId, $messageId, $userId, $params);
                    break;
                case 'confirm_pay':
                    $this->handleConfirmPay($chatId, $messageId, $userId, $params);
                    break;
                case 'mark_failed':
                    $this->handleMarkFailed($chatId, $messageId, $userId, $params);
                    break;
                case 'confirm_daifu':
                    $this->handleConfirmDaifu($chatId, $messageId, $userId, $params);
                    break;
                case 'mark_daifu_failed':
                    $this->handleMarkDaifuFailed($chatId, $messageId, $userId, $params);
                    break;
                case 'daifu_detail':
                    $this->handleDaifuDetail($chatId, $messageId, $userId, $params);
                    break;
                // 新增状态按钮处理
                case 'status_unpaid':
                    $this->handleStatusUpdate($chatId, $messageId, $userId, $params, '未支付', '❓');
                    break;
                case 'status_timeout':
                    $this->handleStatusUpdate($chatId, $messageId, $userId, $params, '超时支付', '⏰');
                    break;
                case 'status_amount_error':
                    $this->handleStatusUpdate($chatId, $messageId, $userId, $params, '金额不符', '💲');
                    break;
                case 'status_image_error':
                    $this->handleStatusUpdate($chatId, $messageId, $userId, $params, '单图不符', '🚫');
                    break;
                case 'status_video_verify':
                    $this->handleStatusUpdate($chatId, $messageId, $userId, $params, '视频核实', '🎬');
                    break;
                // 异常通知静音
                case 'mute_exception':
                    $this->handleMuteException($chatId, $messageId, $userId, $params);
                    break;
                default:                    $this->handleUnknownCallback($chatId, $messageId);
                    break;
            }

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 处理失败');
        }
    }

    /**
     * 处理回调查询
     *
     * @param CallbackQuery $callbackQuery
     * @return void
     */
    public function handleCallback(CallbackQuery $callbackQuery): void
    {
        $data = $callbackQuery->getData();
        $chatId = $callbackQuery->getMessage()->getChat()->getId();
        $messageId = $callbackQuery->getMessage()->getMessageId();
        $userId = $callbackQuery->getFrom()->getId();
        $callbackId = $callbackQuery->getId();

        // 调用通用的handle方法
        $this->handle($callbackId, $data, $chatId, $messageId, $userId);
    }
    
    /**
     * 处理抢单回调
     */
    protected function handleGrabOrder(int $chatId, int $messageId, int $userId, string $orderId): void
    {
        if (!$this->checkOperatorPermission($userId, $this->currentBotId)) {
            $this->telegramService->getApi()->answerCallbackQuery(
                '',
                '❌ 权限不足',
                true
            );
            return;
        }
        
        $order = DaifuOrder::find($orderId);
        if (!$order) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 订单不存在');
            return;
        }
        
        if ($order->status !== DaifuOrder::STATUS_WAIT) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 订单已被抢单或已过期');
            return;
        }

        Db::startTrans();
        try {
            // 更新订单状态
            $order->status = DaifuOrder::STATUS_PROCESSING;
            $order->grab_user_id = $userId;
            $order->grab_time = time();
            $order->save();
            
            // 更新消息
            $newText = "✅ <b>订单已被抢单</b>\n\n";
            $newText .= "📋 订单号: <code>{$order->order_no}</code>\n";
            $newText .= "💰 金额: ¥{$order->amount}\n";
            $newText .= "👤 抢单人: " . $this->getUserName($userId) . "\n";
            $newText .= "⏰ 抢单时间: " . date('Y-m-d H:i:s') . "\n";
            
            $buttons = [
                [
                    ['text' => '✅ 确认完成', 'callback_data' => "complete_order:{$order->id}"],
                    ['text' => '❌ 标记失败', 'callback_data' => "fail_order:{$order->id}"]
                ]
            ];
            
            $this->telegramService->editMessage($chatId, $messageId, $newText, $buttons);
            
            Db::commit();

            // 记录日志
            $this->logOperation('抢单成功', $userId, $order->order_no);

        } catch (\Exception $e) {
            Db::rollback();
            $this->updateMessageWithError($chatId, $messageId, '❌ 抢单失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 处理查看订单详情回调
     */
    protected function handleViewOrder(int $chatId, int $messageId, int $userId, string $orderId): void
    {
        $order = DaifuOrder::find($orderId);
        if (!$order) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 订单不存在');
            return;
        }
        
        $statusText = $this->getDaifuOrderStatusText($order->status);
        $statusEmoji = $this->getDaifuOrderStatusEmoji($order->status);
        
        $message = "{$statusEmoji} <b>代付订单详情</b>\n\n";
        $message .= "📋 订单号: <code>{$order->order_no}</code>\n";
        $message .= "💰 金额: ¥{$order->amount}\n";
        $message .= "💸 手续费: ¥{$order->fee}\n";
        $message .= "💵 实际金额: ¥{$order->real_amount}\n";
        $message .= "🏪 商户: {$order->merchant_name}\n";
        $message .= "🏦 收款人: {$order->account_name}\n";
        $message .= "💳 收款账号: {$order->account_number}\n";
        $message .= "🏛️ 开户行: {$order->bank_name}\n";
        $message .= "📱 状态: {$statusText}\n";
        $message .= "⏰ 创建时间: " . date('Y-m-d H:i:s', $order->create_time) . "\n";
        
        if ($order->grab_time) {
            $message .= "👤 抢单人: " . $this->getUserName($order->grab_user_id) . "\n";
            $message .= "⏰ 抢单时间: " . date('Y-m-d H:i:s', $order->grab_time) . "\n";
        }
        
        if ($order->complete_time) {
            $message .= "✅ 完成时间: " . date('Y-m-d H:i:s', $order->complete_time) . "\n";
        }
        
        $buttons = [];
        if ($order->status === DaifuOrder::STATUS_WAIT && $this->checkOperatorPermission($userId, $this->currentBotId)) {
            $buttons[] = [
                ['text' => '抢单', 'callback_data' => "grab_order:{$order->id}"]
            ];
        }
        
        $this->telegramService->editMessage($chatId, $messageId, $message, $buttons);
    }
    
    /**
     * 处理审核通过结算回调
     */
    protected function handleApproveSettlement(int $chatId, int $messageId, int $userId, string $settlementId): void
    {
        if (!$this->checkAdminPermission($userId, $this->currentBotId)) {
            $this->telegramService->getApi()->answerCallbackQuery(
                '',
                '❌ 权限不足',
                true
            );
            return;
        }
        
        $settlement = Settlement::find($settlementId);
        if (!$settlement) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 结算记录不存在');
            return;
        }
        
        if ($settlement->status !== Settlement::STATUS_PENDING) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 结算记录状态不正确');
            return;
        }

        Db::startTrans();
        try {
            // 更新结算状态
            $settlement->status = Settlement::STATUS_AUDITED;
            $settlement->audit_user_id = $userId;
            $settlement->audit_time = time();
            $settlement->audit_remark = '管理员审核通过';
            $settlement->save();
            
            // 更新消息
            $newText = "✅ <b>结算审核通过</b>\n\n";
            $newText .= "📋 结算单号: <code>{$settlement->settlement_no}</code>\n";
            $newText .= "💰 结算金额: ¥{$settlement->amount}\n";
            $newText .= "🏪 商户: {$settlement->merchant_name}\n";
            $newText .= "👤 审核人: " . $this->getUserName($userId) . "\n";
            $newText .= "⏰ 审核时间: " . date('Y-m-d H:i:s') . "\n";
            
            $this->telegramService->editMessage($chatId, $messageId, $newText);
            
            Db::commit();

            // 记录日志
            $this->logOperation('审核结算通过', $userId, $settlement->settlement_no);

        } catch (\Exception $e) {
            Db::rollback();
            $this->updateMessageWithError($chatId, $messageId, '❌ 审核失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 处理审核拒绝结算回调
     */
    protected function handleRejectSettlement(int $chatId, int $messageId, int $userId, string $settlementId): void
    {
        if (!$this->checkAdminPermission($userId, $this->currentBotId)) {
            $this->telegramService->getApi()->answerCallbackQuery(
                '',
                '❌ 权限不足',
                true
            );
            return;
        }
        
        $settlement = Settlement::find($settlementId);
        if (!$settlement) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 结算记录不存在');
            return;
        }
        
        if ($settlement->status !== Settlement::STATUS_PENDING) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 结算记录状态不正确');
            return;
        }
        
        Db::startTrans();
        try {
            // 更新结算状态
            $settlement->status = Settlement::STATUS_REJECTED;
            $settlement->audit_user_id = $userId;
            $settlement->audit_time = time();
            $settlement->audit_remark = '管理员审核拒绝';
            $settlement->save();
            
            // 更新消息
            $newText = "❌ <b>结算审核拒绝</b>\n\n";
            $newText .= "📋 结算单号: <code>{$settlement->settlement_no}</code>\n";
            $newText .= "💰 结算金额: ¥{$settlement->amount}\n";
            $newText .= "🏪 商户: {$settlement->merchant_name}\n";
            $newText .= "👤 审核人: " . $this->getUserName($userId) . "\n";
            $newText .= "⏰ 审核时间: " . date('Y-m-d H:i:s') . "\n";
            
            $this->telegramService->editMessage($chatId, $messageId, $newText);
            
            Db::commit();

            // 记录日志
            $this->logOperation('审核结算拒绝', $userId, $settlement->settlement_no);

        } catch (\Exception $e) {
            Db::rollback();
            $this->updateMessageWithError($chatId, $messageId, '❌ 审核失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 处理回复查询回调
     */
    protected function handleReplyQuery(int $chatId, int $messageId, int $userId, string $queryId): void
    {
        $query = BotQuery::find($queryId);
        if (!$query) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 查询记录不存在');
            return;
        }
        
        // 这里可以实现查询回复的逻辑
        // 例如弹出输入框让用户输入回复内容
        $message = "💬 <b>回复查询</b>\n\n";
        $message .= "📋 查询内容: {$query->query_content}\n";
        $message .= "⏰ 查询时间: " . date('Y-m-d H:i:s', $query->create_time) . "\n\n";
        $message .= "请直接回复此消息来回答查询";
        
        $this->telegramService->editMessage($chatId, $messageId, $message);
    }
    
    /**
     * 处理订单列表翻页回调
     */
    protected function handleOrdersPagination(int $chatId, int $messageId, int $userId, string $page): void
    {
        $pageNum = (int)$page;
        $limit = 10;
        $offset = ($pageNum - 1) * $limit;

        $orders = \app\common\model\Order::order('create_time desc')
            ->limit($offset, $limit)
            ->select();

        if ($orders->isEmpty()) {
            $this->updateMessageWithError($chatId, $messageId, '📋 暂无订单数据');
            return;
        }

        $message = "📋 <b>订单列表</b> (第 {$pageNum} 页)\n\n";

        foreach ($orders as $order) {
            $statusEmoji = $this->getOrderStatusEmoji($order->status);
            $message .= "{$statusEmoji} <code>{$order->order_no}</code>\n";
            $message .= "💰 ¥{$order->amount} | {$order->merchant_name}\n";
            $message .= "⏰ " . date('m-d H:i', $order->create_time) . "\n\n";
        }

        // 添加翻页按钮
        $buttons = [];
        if ($pageNum > 1) {
            $buttons[] = ['text' => '⬅️ 上一页', 'callback_data' => "orders:" . ($pageNum - 1)];
        }
        $buttons[] = ['text' => '➡️ 下一页', 'callback_data' => "orders:" . ($pageNum + 1)];

        $this->telegramService->editMessage($chatId, $messageId, $message, [$buttons]);
    }
    
    /**
     * 处理确认操作回调
     */
    protected function handleConfirmAction(int $chatId, int $messageId, int $userId, string $params): void
    {
        // 解析确认参数
        $parts = explode('|', $params);
        $action = $parts[0];
        $targetId = $parts[1] ?? '';
        
        switch ($action) {
            case 'complete_order':
                $this->handleCompleteOrder($chatId, $messageId, $userId, $targetId);
                break;
            case 'fail_order':
                $this->handleFailOrder($chatId, $messageId, $userId, $targetId);
                break;
            default:
                $this->updateMessageWithError($chatId, $messageId, '❌ 未知的确认操作');
        }
    }
    
    /**
     * 处理未知回调
     */
    protected function handleUnknownCallback(int $chatId, int $messageId): void
    {
        $this->updateMessageWithError($chatId, $messageId, '❓ 未知的回调操作');
    }
    
    /**
     * 更新消息为错误信息
     */
    protected function updateMessageWithError(int $chatId, int $messageId, string $error): void
    {
        try {
            $this->telegramService->editMessage($chatId, $messageId, $error);
        } catch (\Exception $e) {        }
    }
    

    

    
    /**
     * 处理转发订单到通道群
     */
    protected function handleForwardOrder(int $chatId, int $messageId, int $userId, string $orderNo): void
    {        if (!$this->checkOperatorPermission($userId, $this->currentBotId)) {
            $this->telegramService->getApi()->answerCallbackQuery(
                '',
                '❌ 权限不足',
                true
            );
            return;
        }

        try {
            // 创建 OrderForwardService 实例
            $forwardService = new OrderForwardService();
            $forwardService->setCurrentBotId($this->currentBotId);

            // 查询订单信息
            $orderInfo = $forwardService->queryOrderData($orderNo);
            if (!$orderInfo) {
                $this->telegramService->getApi()->answerCallbackQuery(
                    '',
                    '❌ 订单不存在',
                    true
                );
                return;
            }

            // 获取原始消息（用于包含图片等信息）
            $originalMessage = null; // 这里可以根据需要获取原始消息

            // 转发到通道群
            $forwardService->forwardOrderToChannel($chatId, $orderInfo, $originalMessage, $messageId);

            $this->telegramService->getApi()->answerCallbackQuery(
                '',
                '✅ 已转发到通道群',
                false
            );

        } catch (\Exception $e) {            $this->telegramService->getApi()->answerCallbackQuery(
                '',
                '❌ 转发失败，请稍后重试',
                true
            );
        }
    }

    /**
     * 处理查单请求
     */
    protected function handleQueryOrder(string $callbackId, int $chatId, int $messageId, int $userId, string $orderNo): void
    {        if (!$this->checkOperatorPermission($userId, $this->currentBotId)) {
            $this->telegramService->answerCallbackQuery($callbackId, '❌ 权限不足', true);
            return;
        }

        try {
            // 创建 OrderForwardService 实例
            $forwardService = new OrderForwardService();
            $forwardService->setCurrentBotId($this->currentBotId);

            // 查询订单信息
            $orderInfo = $forwardService->queryOrderData($orderNo);
            if (!$orderInfo) {
                $this->telegramService->answerCallbackQuery($callbackId, '❌ 订单不存在', true);
                return;
            }

            // 转发到通道群
            $forwardService->forwardOrderToChannel($chatId, $orderInfo, null, $messageId);

            // 更新按钮为催单按钮
            $this->updateButtonToUrge($chatId, $messageId, $orderNo);

            // 记录转发时间，用于催单功能
            $this->recordForwardTime($orderNo, $chatId, $messageId);

            $this->telegramService->answerCallbackQuery($callbackId, '✅ 已转发到通道群', false);

        } catch (\Exception $e) {            $this->telegramService->answerCallbackQuery($callbackId, '❌ 查单失败，请稍后重试', true);
        }
    }









    /**
     * 更新按钮为催单按钮
     */
    protected function updateButtonToUrge(int $chatId, int $messageId, string $orderNo): void
    {
        try {
            // 创建催单按钮
            $buttons = [
                [
                    ['text' => '⏰ 催单', 'callback_data' => "urge_order:{$orderNo}"]
                ]
            ];

            // 更新消息按钮
            $this->telegramService->getApi()->editMessageReplyMarkup(
                $chatId,
                $messageId,
                new \TelegramBot\Api\Types\Inline\InlineKeyboardMarkup($buttons)
            );

        } catch (\Exception $e) {        }
    }

    /**
     * 记录转发时间
     */
    protected function recordForwardTime(string $orderNo, int $chatId, int $messageId): void
    {
        try {
            // 使用缓存记录转发时间和消息信息
            $cacheKey = "urge_order:{$orderNo}";
            $data = [
                'forward_time' => time(),
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'urge_count' => 0,
                'last_urge_time' => 0
            ];

            // 缓存15分钟（催单的最大时长）
            cache($cacheKey, $data, 900);

        } catch (\Exception $e) {        }
    }

    /**
     * 处理催单请求
     */
    protected function handleUrgeOrder(string $callbackId, int $chatId, int $messageId, int $userId, string $orderNo): void
    {
        \think\facade\Log::info("handleUrgeOrder被调用: orderNo={$orderNo}, userId={$userId}");

        if (!$this->checkOperatorPermission($userId, $this->currentBotId)) {
            \think\facade\Log::info("催单权限检查失败: userId={$userId}");
            $this->telegramService->answerCallbackQuery($callbackId, '❌ 权限不足', true);
            return;
        }

        \think\facade\Log::info("催单权限检查通过");

        try {
            $cacheKey = "urge_order:{$orderNo}";
            $data = cache($cacheKey);

            if (!$data) {
                \think\facade\Log::info("催单信息已过期: orderNo={$orderNo}");
                $this->telegramService->answerCallbackQuery($callbackId, '❌ 催单信息已过期', true);
                return;
            }

            \think\facade\Log::info("催单缓存数据获取成功");

            $currentTime = time();
            $forwardTime = $data['forward_time'];
            $lastUrgeTime = $data['last_urge_time'];
            $urgeCount = $data['urge_count'];

            // 检查是否超过15分钟
            if ($currentTime - $forwardTime > 900) { // 15分钟 = 900秒
                \think\facade\Log::info("催单时间已结束: orderNo={$orderNo}");
                $this->telegramService->answerCallbackQuery($callbackId, '⏰ 催单时间已结束（15分钟）', true);
                return;
            }

            \think\facade\Log::info("催单时间检查通过");

            // 检查是否距离上次催单不足5分钟
            if ($lastUrgeTime > 0 && $currentTime - $lastUrgeTime < 300) { // 5分钟 = 300秒
                $remainingTime = 300 - ($currentTime - $lastUrgeTime);
                $minutes = floor($remainingTime / 60);
                $seconds = $remainingTime % 60;
                $this->telegramService->answerCallbackQuery($callbackId, "⏰ 请等待 {$minutes}分{$seconds}秒后再催单", true);
                return;
            }

            // 执行催单
            $this->performUrge($orderNo, $data);

            // 更新催单记录
            $data['urge_count'] = $urgeCount + 1;
            $data['last_urge_time'] = $currentTime;
            cache($cacheKey, $data, 900 - ($currentTime - $forwardTime));

            $this->telegramService->answerCallbackQuery($callbackId, '✅ 催单成功', false);

        } catch (\Exception $e) {            $this->telegramService->answerCallbackQuery($callbackId, '❌ 催单失败，请稍后重试', true);
        }
    }

    /**
     * 执行催单操作
     */
    protected function performUrge(string $orderNo, array $data): void
    {
        try {
            \think\facade\Log::info("开始执行催单: orderNo={$orderNo}");

            // 从缓存中获取转发信息（更可靠）
            $channelGroupId = $data['channel_group_id'];
            $forwardMessageId = $data['message_id'];

            \think\facade\Log::info("转发信息: channelGroupId={$channelGroupId}, forwardMessageId={$forwardMessageId}");

            if (!$channelGroupId || !$forwardMessageId) {
                \think\facade\Log::info("转发信息不完整，催单失败");
                return;
            }

            // 发送催单消息（回复通道群中之前转发的消息）
            $urgeMessage = "⏰ <b>催单提醒</b>\n\n";
            $urgeMessage .= "📝 订单号: <code>{$orderNo}</code>\n";
            $urgeMessage .= "⚡ 请尽快处理此订单，商户正在等待";

            \think\facade\Log::info("准备发送催单消息到通道群");

            $this->telegramService->sendMessage($channelGroupId, $urgeMessage, [
                'reply_to_message_id' => $forwardMessageId,
                'parse_mode' => 'HTML'
            ]);

            \think\facade\Log::info("催单消息发送完成");

        } catch (\Exception $e) {
            \think\facade\Log::error("催单执行失败: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取用户名称
     */
    protected function getUserName(int $userId): string
    {
        $admin = BotAdmin::where('telegram_id', $userId)->find();
        return $admin ? $admin->name : "用户{$userId}";
    }
    
    /**
     * 获取代付订单状态文本
     */
    protected function getDaifuOrderStatusText(int $status): string
    {
        $texts = [
            DaifuOrder::STATUS_WAIT => '待抢单',
            DaifuOrder::STATUS_PROCESSING => '处理中',
            DaifuOrder::STATUS_PROCESSED => '已处理待确认',
            DaifuOrder::STATUS_SUCCESS => '成功',
            DaifuOrder::STATUS_FAILED => '失败',
            DaifuOrder::STATUS_CANCELED => '已取消'
        ];

        return $texts[$status] ?? '未知';
    }
    
    /**
     * 获取代付订单状态表情符号
     */
    protected function getDaifuOrderStatusEmoji(int $status): string
    {
        $emojis = [
            DaifuOrder::STATUS_WAIT => '⏳',
            DaifuOrder::STATUS_PROCESSING => '⚡',
            DaifuOrder::STATUS_PROCESSED => '👋',
            DaifuOrder::STATUS_SUCCESS => '✅',
            DaifuOrder::STATUS_FAILED => '❌',
            DaifuOrder::STATUS_CANCELED => '⏰'
        ];

        return $emojis[$status] ?? '❓';
    }

    /**
     * 获取订单状态表情符号
     */
    protected function getOrderStatusEmoji(int $status): string
    {
        $emojis = [
            0 => '⏳', // 待支付
            1 => '⚡', // 处理中
            2 => '✅', // 已支付
            3 => '❌', // 失败
            4 => '⏰'  // 超时
        ];

        return $emojis[$status] ?? '❓';
    }
    
    /**
     * 处理完成订单
     */
    protected function handleCompleteOrder(int $chatId, int $messageId, int $userId, string $orderId): void
    {
        $order = DaifuOrder::find($orderId);
        if (!$order) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 订单不存在');
            return;
        }
        
        if ($order->grab_user_id !== $userId) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 只有抢单人才能操作');
            return;
        }
        
        Db::startTrans();
        try {
            $order->status = DaifuOrder::STATUS_SUCCESS;
            $order->complete_time = time();
            $order->save();
            
            $newText = "✅ <b>订单已完成</b>\n\n";
            $newText .= "📋 订单号: <code>{$order->order_no}</code>\n";
            $newText .= "💰 金额: ¥{$order->amount}\n";
            $newText .= "👤 操作人: " . $this->getUserName($userId) . "\n";
            $newText .= "⏰ 完成时间: " . date('Y-m-d H:i:s') . "\n";
            
            $this->telegramService->editMessage($chatId, $messageId, $newText);
            
            Db::commit();
            
        } catch (\Exception $e) {
            Db::rollback();
            $this->updateMessageWithError($chatId, $messageId, '❌ 操作失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 处理订单失败
     */
    protected function handleFailOrder(int $chatId, int $messageId, int $userId, string $orderId): void
    {
        $order = DaifuOrder::find($orderId);
        if (!$order) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 订单不存在');
            return;
        }
        
        if ($order->grab_user_id !== $userId) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 只有抢单人才能操作');
            return;
        }
        
        Db::startTrans();
        try {
            $order->status = DaifuOrder::STATUS_FAILED;
            $order->fail_time = time();
            $order->fail_reason = '操作员标记失败';
            $order->save();
            
            $newText = "❌ <b>订单已标记失败</b>\n\n";
            $newText .= "📋 订单号: <code>{$order->order_no}</code>\n";
            $newText .= "💰 金额: ¥{$order->amount}\n";
            $newText .= "👤 操作人: " . $this->getUserName($userId) . "\n";
            $newText .= "⏰ 失败时间: " . date('Y-m-d H:i:s') . "\n";
            
            $this->telegramService->editMessage($chatId, $messageId, $newText);
            
            Db::commit();
            
        } catch (\Exception $e) {
            Db::rollback();
            $this->updateMessageWithError($chatId, $messageId, '❌ 操作失败: ' . $e->getMessage());
        }
    }

    /**
     * 处理发送通知到所有群组
     */
    protected function handleNotifyAll(int $chatId, int $messageId, int $userId, string $params): void
    {
        // 检查管理员权限
        if (!$this->checkAdminPermission($userId, $this->currentBotId)) {
            $this->telegramService->getApi()->answerCallbackQuery(
                '',
                '❌ 权限不足',
                true
            );
            return;
        }

        // 解码通知内容
        $content = base64_decode($params);
        if (empty($content)) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 通知内容为空');
            return;
        }

        // 调用 NotificationService 的发送方法
        $notificationService = new NotificationService();
        $notificationService->sendNotifyToAllGroups($chatId, $userId, $content);

        // 更新原消息
        $this->telegramService->editMessage($chatId, $messageId, "✅ 通知发送任务已启动，请查看发送结果");
    }

    /**
     * 处理选择指定商户发送通知
     */
    protected function handleNotifySelect(int $chatId, int $messageId, int $userId, string $params): void
    {
        // 检查管理员权限
        if (!$this->checkAdminPermission($userId, $this->currentBotId)) {
            $this->telegramService->getApi()->answerCallbackQuery(
                '',
                '❌ 权限不足',
                true
            );
            return;
        }

        // 解码通知内容
        $content = base64_decode($params);
        if (empty($content)) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 通知内容为空');
            return;
        }

        // 获取所有绑定的商户群
        $binds = \app\common\model\BotBind::where('type', \app\common\model\BotBind::USER_TYPE_MERCHANT)
            ->where('status', 1)
            ->select();

        if ($binds->isEmpty()) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 没有找到绑定的商户群');
            return;
        }

        $message = "🎯 <b>选择发送目标</b>\n\n";
        $message .= "📝 <b>通知内容：</b>\n";
        $message .= "<code>" . htmlspecialchars($content) . "</code>\n\n";
        $message .= "请选择要发送的商户群：";

        // 创建商户选择按钮（每行2个）
        $keyboard = [];
        $row = [];
        foreach ($binds as $index => $bind) {
            $merchantName = mb_substr($bind->username, 0, 8); // 限制显示长度
            $row[] = [
                'text' => $merchantName,
                'callback_data' => 'notify_merchant:' . $bind->id . '|' . base64_encode($content)
            ];

            // 每行2个按钮
            if (count($row) == 2 || $index == $binds->count() - 1) {
                $keyboard[] = $row;
                $row = [];
            }
        }

        // 添加取消按钮
        $keyboard[] = [
            ['text' => '❌ 取消', 'callback_data' => 'notify_cancel:']
        ];

        $this->telegramService->editMessage($chatId, $messageId, $message, $keyboard);
    }

    /**
     * 处理取消通知
     */
    protected function handleNotifyCancel(int $chatId, int $messageId, int $userId): void
    {
        $this->telegramService->editMessage($chatId, $messageId, "❌ 通知发送已取消");
    }

    /**
     * 处理发送通知到指定商户
     */
    protected function handleNotifyMerchant(int $chatId, int $messageId, int $userId, string $params): void
    {
        // 检查管理员权限
        if (!$this->checkAdminPermission($userId, $this->currentBotId)) {
            $this->telegramService->getApi()->answerCallbackQuery(
                '',
                '❌ 权限不足',
                true
            );
            return;
        }

        // 解析参数：bindId|content
        $parts = explode('|', $params, 2);
        if (count($parts) != 2) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 参数格式错误');
            return;
        }

        $bindId = $parts[0];
        $content = base64_decode($parts[1]);

        if (empty($content)) {
            $this->updateMessageWithError($chatId, $messageId, '❌ 通知内容为空');
            return;
        }

        // 调用 NotificationService 的发送方法
        $notificationService = new NotificationService();
        $notificationService->sendNotifyToMerchant($chatId, $userId, $bindId, $content);

        // 更新原消息
        $this->telegramService->editMessage($chatId, $messageId, "✅ 通知发送任务已启动，请查看发送结果");
    }



    /**
     * 处理联系客服操作
     */
    protected function handleContactService(int $chatId, int $messageId, int $userId, string $orderNo): void
    {
        try {
            $message = "📞 <b>联系客服</b>\n\n";
            $message .= "📝 订单号: <code>{$orderNo}</code>\n\n";
            $message .= "请联系以下客服:\n";
            $message .= "• 客服QQ: 123456789\n";
            $message .= "• 客服微信: service_wechat\n";
            $message .= "• 工作时间: 9:00-18:00\n\n";
            $message .= "💡 请提供订单号以便快速处理";

            $this->telegramService->editMessage($chatId, $messageId, $message);

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 获取客服信息失败');
        }
    }

    /**
     * 处理重新处理订单操作
     */
    protected function handleRetryOrder(int $chatId, int $messageId, int $userId, string $orderNo): void
    {
        try {            $message = "🔄 <b>重新处理订单</b>\n\n";
            $message .= "📝 订单号: <code>{$orderNo}</code>\n";
            $message .= "👤 操作人: {$userId}\n";
            $message .= "⏰ 操作时间: " . date('Y-m-d H:i:s') . "\n\n";
            $message .= "✅ 订单已提交重新处理";

            $this->telegramService->editMessage($chatId, $messageId, $message);

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 重新处理失败');
        }
    }

    /**
     * 处理查看失败原因操作
     */
    protected function handleCheckReason(int $chatId, int $messageId, int $userId, string $orderNo): void
    {
        try {
            // 这里可以查询订单的失败原因
            $message = "❓ <b>订单失败原因</b>\n\n";
            $message .= "📝 订单号: <code>{$orderNo}</code>\n\n";
            $message .= "失败原因:\n";
            $message .= "• 支付超时\n";
            $message .= "• 银行卡余额不足\n";
            $message .= "• 网络连接异常\n\n";
            $message .= "💡 如需帮助请联系客服";

            $this->telegramService->editMessage($chatId, $messageId, $message);

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 获取失败原因失败');
        }
    }

    /**
     * 处理订单详情操作
     */
    protected function handleOrderDetail(int $chatId, int $messageId, int $userId, string $orderNo): void
    {
        try {
            // 调用 OrderForwardService 获取订单详情
            $orderForwardService = new \app\telegram\service\OrderForwardService();

            // 这里可以获取更详细的订单信息
            $message = "🔍 <b>订单详细信息</b>\n\n";
            $message .= "📝 订单号: <code>{$orderNo}</code>\n";
            $message .= "💰 金额: 待查询\n";
            $message .= "📊 状态: 待查询\n";
            $message .= "⏰ 创建时间: 待查询\n";
            $message .= "🔄 更新时间: 待查询\n\n";
            $message .= "💡 详细信息查询中...";

            $this->telegramService->editMessage($chatId, $messageId, $message);

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 获取订单详情失败');
        }
    }

    /**
     * 处理订单日志操作
     */
    protected function handleOrderLog(int $chatId, int $messageId, int $userId, string $orderNo): void
    {
        try {
            $message = "📋 <b>订单处理日志</b>\n\n";
            $message .= "📝 订单号: <code>{$orderNo}</code>\n\n";
            $message .= "处理记录:\n";
            $message .= "• " . date('Y-m-d H:i:s') . " 订单创建\n";
            $message .= "• " . date('Y-m-d H:i:s') . " 等待支付\n";
            $message .= "• " . date('Y-m-d H:i:s') . " 处理中\n\n";
            $message .= "💡 完整日志请查看后台系统";

            $this->telegramService->editMessage($chatId, $messageId, $message);

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 获取订单日志失败');
        }
    }

    /**
     * 处理确认处理操作
     */
    protected function handleConfirmProcess(int $chatId, int $messageId, int $userId, string $forwardNo): void
    {
        try {            // 更新转发记录状态
            $record = \app\common\model\BotQuery::where('system_order_no', $forwardNo)
                ->where('status', 0) // 只处理待处理状态的记录
                ->order('id', 'desc')
                ->find();

            if (!$record) {
                $this->telegramService->sendMessage($chatId, '❌ 未找到对应的订单记录或订单已处理', [
                    'reply_to_message_id' => $messageId
                ]);
                return;
            }

            // 更新记录状态
            $record->status = 1; // 已确认
            $record->responder_id = (string)$userId;
            $record->response_time = date('Y-m-d H:i:s');
            $record->remark = "通过按钮确认处理";
            $record->save();

            // 获取商户群ID
            $merchantGroupId = $this->getGroupIdByName($record->from_group_name);
            if (!$merchantGroupId) {                $this->telegramService->sendMessage($chatId, '❌ 无法找到对应的商户群', [
                    'reply_to_message_id' => $messageId
                ]);
                return;
            }

            // 构建发送到商户群的确认消息
            $merchantMessage = "✅ <b>通道确认处理</b>\n\n";
            $merchantMessage .= "📝 订单号: <code>{$forwardNo}</code>\n";
            $merchantMessage .= "👤 确认人: {$userId}\n";
            $merchantMessage .= "⏰ 确认时间: " . date('Y-m-d H:i:s') . "\n\n";
            $merchantMessage .= "🎉 订单处理确认成功";

            // 发送到商户群（回复查询结果消息）
            $this->sendToMerchantGroup($merchantGroupId, $merchantMessage, $record);

            // 在通道群显示简单确认
            $channelMessage = "✅ 已确认处理订单 <code>{$forwardNo}</code>";
            $this->telegramService->sendMessage($chatId, $channelMessage, [
                'reply_to_message_id' => $messageId
            ]);

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 确认处理失败');
        }
    }

    /**
     * 处理拒绝处理操作
     */
    protected function handleRejectProcess(int $chatId, int $messageId, int $userId, string $forwardNo): void
    {
        try {            // 更新转发记录状态
            $record = \app\common\model\BotQuery::where('system_order_no', $forwardNo)
                ->where('status', 0) // 只处理待处理状态的记录
                ->order('id', 'desc')
                ->find();

            if (!$record) {
                $this->telegramService->sendMessage($chatId, '❌ 未找到对应的订单记录或订单已处理', [
                    'reply_to_message_id' => $messageId
                ]);
                return;
            }

            // 更新记录状态
            $record->status = 2; // 已拒绝
            $record->responder_id = (string)$userId;
            $record->response_time = date('Y-m-d H:i:s');
            $record->remark = "通过按钮拒绝处理";
            $record->save();

            // 获取商户群ID
            $merchantGroupId = $this->getGroupIdByName($record->from_group_name);
            if (!$merchantGroupId) {                $this->telegramService->sendMessage($chatId, '❌ 无法找到对应的商户群', [
                    'reply_to_message_id' => $messageId
                ]);
                return;
            }

            // 构建发送到商户群的拒绝消息
            $merchantMessage = "❌ <b>通道拒绝处理</b>\n\n";
            $merchantMessage .= "📝 订单号: <code>{$forwardNo}</code>\n";
            $merchantMessage .= "👤 拒绝人: {$userId}\n";
            $merchantMessage .= "⏰ 拒绝时间: " . date('Y-m-d H:i:s') . "\n\n";
            $merchantMessage .= "💡 订单已标记为拒绝处理";

            // 发送到商户群（回复查询结果消息）
            $this->sendToMerchantGroup($merchantGroupId, $merchantMessage, $record);

            // 在通道群显示简单确认
            $channelMessage = "❌ 已拒绝处理订单 <code>{$forwardNo}</code>";
            $this->telegramService->sendMessage($chatId, $channelMessage, [
                'reply_to_message_id' => $messageId
            ]);

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 拒绝处理失败');
        }
    }

    /**
     * 根据群组名称获取群组ID
     */
    protected function getGroupIdByName(string $groupName): ?int
    {
        try {
            $bind = BotBind::where('group_name', $groupName)
                ->where('type', BotBind::USER_TYPE_MERCHANT)
                ->where('status', 1)
                ->find();

            return $bind ? $bind->group_id : null;

        } catch (\Exception $e) {            return null;
        }
    }

    /**
     * 发送消息到商户群
     */
    protected function sendToMerchantGroup(int $merchantGroupId, string $message, BotQuery $record): void
    {
        try {
            // 尝试回复查询结果消息
            $queryContent = json_decode($record->query_content, true) ?: [];
            $resultMessageId = $queryContent['result_message_id'] ?? 0;
            $originalMessageId = (int)$record->from_message_id;

            // 优先回复查询结果消息，如果没有则回复原始查单消息
            $replyToMessageId = $resultMessageId > 0 ? $resultMessageId : $originalMessageId;

            if ($replyToMessageId > 0) {
                $this->telegramService->sendMessage($merchantGroupId, $message, [
                    'reply_to_message_id' => $replyToMessageId
                ]);
            } else {
                $this->telegramService->sendMessage($merchantGroupId, $message);
            }        } catch (\Exception $e) {        }
    }

    /**
     * 处理状态更新按钮
     */
    protected function handleStatusUpdate(int $chatId, int $messageId, int $userId, string $orderNo, string $statusText, string $emoji): void
    {
        try {            // 查找转发记录
            $record = \app\common\model\BotQuery::where('system_order_no', $orderNo)
                ->where('status', 0) // 只处理待处理状态的记录
                ->order('id', 'desc')
                ->find();

            if (!$record) {
                $this->telegramService->sendMessage($chatId, '❌ 未找到对应的订单记录或订单已处理', [
                    'reply_to_message_id' => $messageId
                ]);
                return;
            }

            // 更新记录状态（使用特殊状态码）
            $statusMap = [
                '未支付' => 10,
                '超时支付' => 11,
                '金额不符' => 12,
                '单图不符' => 13,
                '视频核实' => 14
            ];

            $statusCode = $statusMap[$statusText] ?? 99;

            $record->status = $statusCode;
            $record->responder_id = (string)$userId;
            $record->response_time = date('Y-m-d H:i:s');
            $record->remark = "通过按钮标记为：{$statusText}";
            $record->save();

            // 获取商户群ID并发送状态更新
            $merchantGroupId = $this->getGroupIdByName($record->from_group_name);
            if ($merchantGroupId) {
                // 构建发送到商户群的状态消息
                $merchantMessage = "{$emoji} <b>订单状态更新</b>\n\n";
                $merchantMessage .= "订单号: <code>{$orderNo}</code>\n";
                $merchantMessage .= "状态: {$emoji} {$statusText}\n";
                $merchantMessage .= "👤 操作人: {$userId}\n";
                $merchantMessage .= "更新时间: " . date('Y-m-d H:i:s') . "\n\n";
                $merchantMessage .= "💡 请根据状态信息进行相应处理";

                // 发送到商户群
                $this->sendToMerchantGroup($merchantGroupId, $merchantMessage, $record);
            }

            // 在通道群显示状态确认
            $channelMessage = "{$emoji} 已标记订单 <code>{$orderNo}</code> 为：{$statusText}";
            $this->telegramService->sendMessage($chatId, $channelMessage, [
                'reply_to_message_id' => $messageId
            ]);

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 状态更新失败');
        }
    }

    /**
     * 处理异常通知静音按钮
     */
    protected function handleMuteException(int $chatId, int $messageId, int $userId, string $params): void
    {
        try {
            // 解析参数：格式为 "分钟数:群组ID"
            $parts = explode(':', $params);
            if (count($parts) < 2) {                return;
            }

            $minutes = $parts[0];
            $groupId = (int)$parts[1];            // 调用异常通知服务处理静音
            $exceptionService = new \app\telegram\service\ExceptionNotificationService();
            $exceptionService->handleMuteCallback($chatId, $messageId, $userId, $minutes);

        } catch (\Exception $e) {            $this->updateMessageWithError($chatId, $messageId, '❌ 静音设置失败');
        }
    }
}
