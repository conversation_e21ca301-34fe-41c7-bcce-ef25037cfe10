<?php

namespace app\telegram\service;

use app\common\service\TelegramBotService;
use app\telegram\service\BaseCommandService;
use app\telegram\service\TelegramBotFactory;
use app\common\model\Order;
use app\common\model\DaifuOrder;
use app\common\model\Settlement;
use app\common\model\BotBind;
use app\common\model\BotQuery;
use think\facade\Config;

/**
 * Telegram 通知服务
 */
class TelegramNotifyService extends BaseCommandService
{
    protected $telegramService;

    public function __construct()
    {
        // 使用默认机器人实例，具体通知时会根据需要选择合适的机器人
        try {
            $this->telegramService = TelegramBotFactory::getDefault();
        } catch (\Exception $e) {
            $this->telegramService = null;
        }
    }

    /**
     * 获取机器人状态信息
     * 供 app/admin 调用
     */
    public function getBotStatus($botToken = null): array
    {
        try {
            if ($botToken) {
                // 使用工厂获取指定Token的机器人实例
                $botService = TelegramBotFactory::getByToken($botToken);
                $result = $botService->testBotToken();
                return [
                    'status' => $result ? 'online' : 'offline',
                    'bot_info' => $result,
                    'last_check' => date('Y-m-d H:i:s')
                ];
            } else {
                // 获取默认机器人状态
                if ($this->telegramService) {
                    $result = $this->telegramService->testBotToken();
                    return [
                        'status' => $result ? 'online' : 'offline',
                        'bot_info' => $result,
                        'last_check' => date('Y-m-d H:i:s')
                    ];
                } else {
                    return [
                        'status' => 'error',
                        'message' => '无可用的机器人实例'
                    ];
                }
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * 启动机器人服务
     * 供 app/admin 调用
     */
    public function startBot($botToken): bool
    {
        try {
            // 这里可以实现启动机器人的逻辑
            // 比如设置Webhook、初始化配置等
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 停止机器人
     * 供 admin 控制器调用
     */
    public function stopBot($botToken): bool
    {
        try {
            // 这里可以实现停止机器人的逻辑
            // 比如删除webhook、清除缓存等
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 发送订单通知
     *
     * @param Order $order 订单对象
     * @param string $type 通知类型 (created|paid|failed|timeout)
     * @return bool
     */
    public function sendOrderNotify(Order $order, string $type = 'created'): bool
    {
        try {
            // 解析订单中的TG信息，检查是否来自TG
            $telegramInfo = $this->parseTelegramInfo($order->body);

            // 如果不是来自TG的订单，直接跳过通知
            if (!$telegramInfo) {
                return true; // 返回true表示处理成功（跳过）
            }

            $message = $this->buildOrderMessage($order, $type);
            $chatIds = $this->getNotifyChatIds($order->merchant_id, 'order');

            foreach ($chatIds as $chatId) {
                // 构建发送选项
                $options = [];

                // 如果是原始群组且有消息ID，则回复原始消息
                if ($telegramInfo &&
                    isset($telegramInfo['chat_id']) &&
                    $telegramInfo['chat_id'] == $chatId &&
                    !empty($telegramInfo['message_id'])) {
                    $options['reply_to_message_id'] = $telegramInfo['message_id'];
                }

                $this->telegramService->sendMessage($chatId, $message, $options);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 发送代付订单通知
     *
     * @param DaifuOrder $order 代付订单对象
     * @param string $type 通知类型
     * @param int $replyToMessageId 回复的消息ID（已废弃，改为自动从订单中解析）
     * @return bool
     */
    public function sendDaifuOrderNotify(DaifuOrder $order, string $type = 'created', int $replyToMessageId = 0): bool
    {
        try {
            // 解析订单中的TG信息，检查是否来自TG
            $telegramInfo = $this->parseTelegramInfo($order->body);

            // 如果不是来自TG的订单，直接跳过通知
            if (!$telegramInfo) {
                return true; // 返回true表示处理成功（跳过）
            }

            $message = $this->buildDaifuOrderMessage($order, $type);
            $chatIds = $this->getNotifyChatIds($order->merchant_id, 'daifu');

            foreach ($chatIds as $chatId) {
                $buttons = [];
                if ($type === 'created') {
                    $buttons = [
                        [
                            ['text' => '抢单', 'callback_data' => "grab_order:{$order->id}"],
                            ['text' => '查看详情', 'callback_data' => "view_order:{$order->id}"]
                        ]
                    ];
                }

                // 构建发送选项
                $options = [];

                // 优先使用从订单中解析的消息ID，如果没有则使用传入的参数
                $messageIdToReply = 0;
                if ($telegramInfo &&
                    isset($telegramInfo['chat_id']) &&
                    $telegramInfo['chat_id'] == $chatId &&
                    !empty($telegramInfo['message_id'])) {
                    $messageIdToReply = $telegramInfo['message_id'];
                } elseif ($replyToMessageId > 0) {
                    $messageIdToReply = $replyToMessageId;
                }

                if ($messageIdToReply > 0) {
                    $options['reply_to_message_id'] = $messageIdToReply;
                }

                if (!empty($buttons)) {
                    $this->telegramService->sendMessageWithButtons($chatId, $message, $buttons, $options);
                } else {
                    $this->telegramService->sendMessage($chatId, $message, $options);
                }
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 发送结算通知
     *
     * @param Settlement $settlement 结算对象
     * @param string $type 通知类型
     * @return bool
     */
    public function sendSettlementNotify(Settlement $settlement, string $type = 'created'): bool
    {
        try {
            $message = $this->buildSettlementMessage($settlement, $type);
            $chatIds = $this->getNotifyChatIds($settlement->merchant_id, 'settlement');
            
            foreach ($chatIds as $chatId) {
                $buttons = [];
                if ($type === 'pending' && $this->isAdmin($chatId)) {
                    $buttons = [
                        [
                            ['text' => '✅ 审核通过', 'callback_data' => "approve_settlement:{$settlement->id}"],
                            ['text' => '❌ 审核拒绝', 'callback_data' => "reject_settlement:{$settlement->id}"]
                        ]
                    ];
                }
                
                if (!empty($buttons)) {
                    $this->telegramService->sendMessageWithButtons($chatId, $message, $buttons);
                } else {
                    $this->telegramService->sendMessage($chatId, $message);
                }
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 发送系统公告
     *
     * @param string $announcement 公告内容
     * @return bool
     */
    public function sendSystemAnnouncement(string $announcement): bool
    {
        try {
            // 检查是否启用自动公告
            $autoAnnouncementEnabled = \app\telegram\service\BotConfigHelper::isAutoAnnouncementEnabled(1);

            if (!$autoAnnouncementEnabled) {
                // 如果禁用了自动公告，直接返回成功但不发送
                return true;
            }

            $adminChatIds = $this->getAdminChatIds();

            foreach ($adminChatIds as $chatId) {
                $this->telegramService->sendMessage($chatId, $announcement);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 发送订单通知（兼容方法）
     *
     * @param array $orderData 订单数据
     * @return bool
     */
    public function sendOrderNotification(array $orderData): bool
    {
        try {
            $message = "💰 <b>订单通知</b>\n\n";
            $message .= "📋 订单号: <code>{$orderData['order_no']}</code>\n";
            $message .= "💰 金额: ¥{$orderData['amount']}\n";
            $message .= "📊 状态: {$orderData['status']}\n";
            $message .= "🏪 商户: {$orderData['merchant_name']}\n";
            $message .= "⏰ 时间: " . date('Y-m-d H:i:s', $orderData['create_time']) . "\n";

            $adminChatIds = $this->getAdminChatIds();

            foreach ($adminChatIds as $chatId) {
                $this->telegramService->sendMessage($chatId, $message);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 发送代付通知（兼容方法）
     *
     * @param array $orderData 代付订单数据
     * @return bool
     */
    public function sendDaifuNotification(array $orderData): bool
    {
        try {
            $message = "💸 <b>代付通知</b>\n\n";
            $message .= "📋 订单号: <code>{$orderData['order_no']}</code>\n";
            $message .= "💰 金额: ¥{$orderData['amount']}\n";
            $message .= "📊 状态: {$orderData['status']}\n";
            $message .= "🏪 商户: {$orderData['merchant_name']}\n";
            $message .= "⏰ 时间: " . date('Y-m-d H:i:s', $orderData['create_time']) . "\n";

            $adminChatIds = $this->getAdminChatIds();

            foreach ($adminChatIds as $chatId) {
                $this->telegramService->sendMessage($chatId, $message);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 发送系统警报
     *
     * @param string $title 警报标题
     * @param string $content 警报内容
     * @param string $level 警报级别 (info|warning|error|critical)
     * @return bool
     */
    public function sendSystemAlert(string $title, string $content, string $level = 'info'): bool
    {
        try {
            $emoji = $this->getLevelEmoji($level);
            $message = "{$emoji} <b>{$title}</b>\n\n{$content}\n\n⏰ " . date('Y-m-d H:i:s');

            $adminChatIds = $this->getAdminChatIds();

            foreach ($adminChatIds as $chatId) {
                $this->telegramService->sendMessage($chatId, $message);
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 发送币种支付成功通知到群组
     *
     * @param array $orderData 订单数据
     * @return bool
     */
    public function sendCurrencyPaymentSuccessNotify(array $orderData): bool
    {
        try {
            // 从订单数据中提取群组ID
            $chatId = $this->extractChatIdFromOrderData($orderData);

            if (!$chatId) {
                return false;
            }

            // 构建支付成功消息
            $message = $this->buildCurrencyPaymentSuccessMessage($orderData);

            // 发送消息到群组
            $this->telegramService->sendMessage($chatId, $message);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 发送币种支付失败通知到群组
     *
     * @param array $orderData 订单数据
     * @return bool
     */
    public function sendCurrencyPaymentFailureNotify(array $orderData): bool
    {
        try {
            // 从订单数据中提取群组ID
            $chatId = $this->extractChatIdFromOrderData($orderData);

            if (!$chatId) {
                return false;
            }

            // 构建支付失败消息
            $message = $this->buildCurrencyPaymentFailureMessage($orderData);

            // 发送消息到群组
            $this->telegramService->sendMessage($chatId, $message);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    // 已移除重复的 sendTransferPaymentSuccessNotify 方法
    // 使用 notifyTransferSuccess 方法替代

    /**
     * 发送代付失败通知到群组
     *
     * @param array $orderData 订单数据
     * @return bool
     */
    public function sendTransferPaymentFailureNotify(array $orderData): bool
    {
        try {
            // 从订单数据中提取群组ID
            $chatId = $this->extractChatIdFromOrderData($orderData);

            if (!$chatId) {
                return false;
            }

            // 构建代付失败消息
            $message = $this->buildTransferPaymentFailureMessage($orderData);

            // 发送消息到群组
            $this->telegramService->sendMessage($chatId, $message);
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 构建订单消息
     */
    protected function buildOrderMessage(Order $order, string $type): string
    {
        $emoji = $this->getOrderTypeEmoji($type);
        $statusText = $this->getOrderStatusText($type);
        
        $message = "{$emoji} <b>订单{$statusText}</b>\n\n";
        $message .= "📋 订单号: <code>{$order->order_no}</code>\n";
        $message .= "💰 金额: ¥{$order->amount}\n";
        $message .= "🏪 商户: {$order->merchant_name}\n";
        $message .= "💳 支付方式: {$order->pay_type_name}\n";
        $message .= "⏰ 时间: " . date('Y-m-d H:i:s', $order->create_time) . "\n";
        
        if ($type === 'paid') {
            $message .= "💵 实付金额: ¥{$order->real_amount}\n";
            $message .= "✅ 支付时间: " . date('Y-m-d H:i:s', $order->pay_time) . "\n";
        }
        
        return $message;
    }
    
    /**
     * 构建代付订单消息
     */
    protected function buildDaifuOrderMessage(DaifuOrder $order, string $type): string
    {
        $emoji = $this->getDaifuOrderTypeEmoji($type);
        $statusText = $this->getDaifuOrderStatusText($type);
        
        $message = "{$emoji} <b>代付订单{$statusText}</b>\n\n";
        $message .= "📋 订单号: <code>{$order->order_no}</code>\n";
        $message .= "💰 金额: ¥{$order->amount}\n";
        $message .= "💸 手续费: ¥{$order->fee}\n";
        $message .= "💵 实际金额: ¥{$order->real_amount}\n";
        $message .= "🏪 商户: {$order->merchant_name}\n";
        $message .= "🏦 收款信息: {$order->account_name} - {$order->account_number}\n";
        $message .= "⏰ 时间: " . date('Y-m-d H:i:s', $order->create_time) . "\n";
        
        if ($order->expire_time) {
            $message .= "⏳ 过期时间: " . date('Y-m-d H:i:s', $order->expire_time) . "\n";
        }
        
        return $message;
    }
    
    /**
     * 构建结算消息
     */
    protected function buildSettlementMessage(Settlement $settlement, string $type): string
    {
        $emoji = $this->getSettlementTypeEmoji($type);
        $statusText = $this->getSettlementStatusText($type);
        
        $message = "{$emoji} <b>结算{$statusText}</b>\n\n";
        $message .= "📋 结算单号: <code>{$settlement->settlement_no}</code>\n";
        $message .= "💰 结算金额: ¥{$settlement->amount}\n";
        $message .= "💸 手续费: ¥{$settlement->fee}\n";
        $message .= "💵 实际到账: ¥{$settlement->actual_amount}\n";
        $message .= "🏪 商户: {$settlement->merchant_name}\n";
        $message .= "🏦 结算方式: {$settlement->method_text}\n";
        $message .= "⏰ 申请时间: " . date('Y-m-d H:i:s', $settlement->create_time) . "\n";
        
        return $message;
    }
    
    /**
     * 获取通知聊天ID列表
     */
    protected function getNotifyChatIds(int $merchantId, string $type): array
    {
        // 从配置或数据库获取需要通知的聊天ID
        $chatIds = [];

        // 获取商户绑定的群组（只获取商户类型的绑定）
        $binds = BotBind::where('user_id', $merchantId)
            ->where('type', BotBind::USER_TYPE_MERCHANT) // 只获取商户类型绑定
            ->where('status', 1)
            ->select();

        foreach ($binds as $bind) {
            if (!empty($bind->group_id)) {
                $chatIds[] = $bind->group_id;
            }
        }

        // 移除详细的群组获取日志

        return array_unique($chatIds);
    }
    
    /**
     * 获取管理员聊天ID列表
     */
    protected function getAdminChatIds(): array
    {
        $adminIds = Config::get('telegram.admin_ids', []);
        return array_filter($adminIds, function($id) {
            return !empty($id);
        });
    }
    
    /**
     * 检查是否为管理员
     */
    protected function isAdmin(int $chatId): bool
    {
        $adminIds = $this->getAdminChatIds();
        return in_array($chatId, $adminIds);
    }
    
    /**
     * 获取订单类型表情符号
     */
    protected function getOrderTypeEmoji(string $type): string
    {
        $emojis = [
            'created' => '🆕',
            'paid' => '✅',
            'failed' => '❌',
            'timeout' => '⏰'
        ];
        
        return $emojis[$type] ?? '📋';
    }
    
    /**
     * 获取代付订单类型表情符号
     */
    protected function getDaifuOrderTypeEmoji(string $type): string
    {
        $emojis = [
            'created' => '🆕',
            'grabbed' => '👋',
            'processing' => '⚡',
            'completed' => '✅',
            'failed' => '❌',
            'timeout' => '⏰'
        ];
        
        return $emojis[$type] ?? '💸';
    }
    
    /**
     * 获取结算类型表情符号
     */
    protected function getSettlementTypeEmoji(string $type): string
    {
        $emojis = [
            'created' => '🆕',
            'pending' => '⏳',
            'approved' => '✅',
            'rejected' => '❌',
            'completed' => '💰'
        ];
        
        return $emojis[$type] ?? '💼';
    }
    
    /**
     * 获取警报级别表情符号
     */
    protected function getLevelEmoji(string $level): string
    {
        $emojis = [
            'info' => 'ℹ️',
            'warning' => '⚠️',
            'error' => '❌',
            'critical' => '🚨'
        ];
        
        return $emojis[$level] ?? 'ℹ️';
    }
    
    /**
     * 获取订单状态文本
     */
    protected function getOrderStatusText(string $type): string
    {
        $texts = [
            'created' => '创建',
            'paid' => '支付成功',
            'failed' => '支付失败',
            'timeout' => '超时'
        ];
        
        return $texts[$type] ?? '更新';
    }
    
    /**
     * 获取代付订单状态文本
     */
    protected function getDaifuOrderStatusText(string $type): string
    {
        $texts = [
            'created' => '创建',
            'grabbed' => '已抢单',
            'processing' => '处理中',
            'completed' => '完成',
            'failed' => '失败',
            'timeout' => '超时'
        ];
        
        return $texts[$type] ?? '更新';
    }
    
    /**
     * 获取结算状态文本
     */
    protected function getSettlementStatusText(string $type): string
    {
        $texts = [
            'created' => '申请',
            'pending' => '待审核',
            'approved' => '审核通过',
            'rejected' => '审核拒绝',
            'completed' => '完成'
        ];
        
        return $texts[$type] ?? '更新';
    }

    /**
     * 从订单数据中提取群组ID
     */
    protected function extractChatIdFromOrderData(array $orderData): ?int
    {
        // 方法1: 从商户订单号中提取（如果是TG或TF开头的订单）
        $merchantOrderNo = $orderData['merchant_order_no'] ?? '';
        if (strpos($merchantOrderNo, 'TG') === 0 || strpos($merchantOrderNo, 'TF') === 0) {
            // 从订单备注或body中提取群组ID
            $body = $orderData['body'] ?? '';
            if (preg_match('/群组ID:(-?\d+)/', $body, $matches)) {
                return (int)$matches[1];
            }
        }

        // 方法2: 从订单备注中提取
        $remark = $orderData['remark'] ?? '';
        if (preg_match('/chat_id:(-?\d+)/', $remark, $matches)) {
            return (int)$matches[1];
        }

        // 方法3: 从扩展字段中提取
        $extra = $orderData['extra'] ?? '';
        if (!empty($extra)) {
            $extraData = json_decode($extra, true);
            if (isset($extraData['chat_id'])) {
                return (int)$extraData['chat_id'];
            }
        }

        return null;
    }

    /**
     * 构建币种支付成功消息
     */
    protected function buildCurrencyPaymentSuccessMessage(array $orderData): string
    {
        $amount = $orderData['amount'] ?? '0';
        $currency = $orderData['currency'] ?? '';
        $orderNo = $orderData['order_no'] ?? '';
        $merchantOrderNo = $orderData['merchant_order_no'] ?? '';
        $paidTime = $orderData['paid_time'] ?? date('Y-m-d H:i:s');

        // 获取币种中文名称
        $currencyNames = [
            'BRL' => '巴西雷亚尔',
            'CNY' => '人民币',
            'USD' => '美元',
            'EUR' => '欧元',
            'ARS' => '阿根廷比索',
            'MXN' => '墨西哥比索',
            'COP' => '哥伦比亚比索',
            'PEN' => '秘鲁索尔',
            'CLP' => '智利比索'
        ];

        $currencyName = $currencyNames[$currency] ?? $currency;

        $message = "🎉 <b>支付成功通知</b>\n\n";
        $message .= "✅ 订单支付成功！\n\n";
        $message .= "💰 支付金额: <b>{$amount} {$currency}</b>\n";
        $message .= "💱 支付币种: {$currencyName}\n";
        $message .= "📝 订单号: <code>{$orderNo}</code>\n";
        $message .= "🏪 商户订单号: <code>{$merchantOrderNo}</code>\n";
        $message .= "⏰ 支付时间: {$paidTime}\n\n";
        $message .= "🎊 感谢您的支付！";

        return $message;
    }

    /**
     * 构建币种支付失败消息
     */
    protected function buildCurrencyPaymentFailureMessage(array $orderData): string
    {
        $amount = $orderData['amount'] ?? '0';
        $currency = $orderData['currency'] ?? '';
        $orderNo = $orderData['order_no'] ?? '';
        $merchantOrderNo = $orderData['merchant_order_no'] ?? '';
        $failReason = $orderData['fail_reason'] ?? '未知原因';

        // 获取币种中文名称
        $currencyNames = [
            'BRL' => '巴西雷亚尔',
            'CNY' => '人民币',
            'USD' => '美元',
            'EUR' => '欧元',
            'ARS' => '阿根廷比索',
            'MXN' => '墨西哥比索',
            'COP' => '哥伦比亚比索',
            'PEN' => '秘鲁索尔',
            'CLP' => '智利比索'
        ];

        $currencyName = $currencyNames[$currency] ?? $currency;

        $message = "❌ <b>支付失败通知</b>\n\n";
        $message .= "💰 支付金额: {$amount} {$currency}\n";
        $message .= "💱 支付币种: {$currencyName}\n";
        $message .= "📝 订单号: <code>{$orderNo}</code>\n";
        $message .= "🏪 商户订单号: <code>{$merchantOrderNo}</code>\n";
        $message .= "❗ 失败原因: {$failReason}\n\n";
        $message .= "💡 如需重新支付，请重新发起支付命令";

        return $message;
    }

    // 已移除重复的 buildTransferPaymentSuccessMessage 方法
    // 使用 buildTransferSuccessMessage 方法替代

    /**
     * 构建代付失败消息
     */
    protected function buildTransferPaymentFailureMessage(array $orderData): string
    {
        $amount = $orderData['amount'] ?? '0';
        $currency = $orderData['currency'] ?? '';
        $orderNo = $orderData['order_no'] ?? '';
        $merchantOrderNo = $orderData['merchant_order_no'] ?? '';
        $failReason = $orderData['fail_reason'] ?? '未知原因';

        // 获取币种中文名称
        $currencyNames = [
            'BRL' => '巴西雷亚尔',
            'CNY' => '人民币',
            'USD' => '美元',
            'EUR' => '欧元',
            'INR' => '印度卢比',
            'PHP' => '菲律宾比索'
        ];

        $currencyName = $currencyNames[$currency] ?? $currency;

        // 从订单备注中提取收款人信息
        $recipientInfo = $this->extractRecipientInfo($orderData);

        $message = "❌ <b>代付失败通知</b>\n\n";
        $message .= "💰 代付金额: {$amount} {$currency}\n";
        $message .= "💱 代付币种: {$currencyName}\n";

        if ($recipientInfo) {
            $message .= "👤 收款人: {$recipientInfo['name']}\n";
            $message .= "🔑 {$recipientInfo['account_type']}: {$recipientInfo['account']}\n";
        }

        $message .= "📝 订单号: <code>{$orderNo}</code>\n";
        $message .= "🏪 商户订单号: <code>{$merchantOrderNo}</code>\n";
        $message .= "❗ 失败原因: {$failReason}\n\n";
        $message .= "💡 如需重新代付，请重新发起代付命令";

        return $message;
    }

    /**
     * 从订单数据中提取收款人信息
     */
    protected function extractRecipientInfo(array $orderData): ?array
    {
        // 从订单备注中提取收款人信息
        $body = $orderData['body'] ?? '';

        // 解析方式信息 (如: 方式:pix)
        $method = '';
        if (preg_match('/方式:(\w+)/', $body, $matches)) {
            $method = $matches[1];
        }

        // 从扩展字段中获取收款人信息
        $extra = $orderData['extra'] ?? '';
        if (!empty($extra)) {
            $extraData = json_decode($extra, true);
            if (is_array($extraData) && isset($extraData['extra_params'])) {
                $extraParams = $extraData['extra_params'];

                $name = $extraParams['name'] ?? '';

                // 根据支付方式确定账号类型和账号
                switch (strtolower($method)) {
                    case 'pix':
                        foreach (['CPF', 'CNPJ', 'EMAIL', 'PHONE', 'RANDOM_KEY'] as $type) {
                            if (isset($extraParams[$type])) {
                                return [
                                    'name' => $name,
                                    'account' => $extraParams[$type],
                                    'account_type' => 'PIX'
                                ];
                            }
                        }
                        break;
                    case 'upi':
                        if (isset($extraParams['UPI'])) {
                            return [
                                'name' => $name,
                                'account' => $extraParams['UPI'],
                                'account_type' => 'UPI'
                            ];
                        }
                        break;
                    case 'gcash':
                        if (isset($extraParams['MOBILE'])) {
                            return [
                                'name' => $name,
                                'account' => $extraParams['MOBILE'],
                                'account_type' => 'GCash'
                            ];
                        }
                        break;
                }
            }
        }

        return null;
    }

    /**
     * 通知订单支付成功（TG内部调用）
     *
     * @param \app\common\model\Order $order 订单对象
     * @return bool 是否成功
     */
    public function notifyOrderSuccess(\app\common\model\Order $order): bool
    {
        try {
            // 1. 解析订单中的TG信息，检查是否来自TG
            $telegramInfo = $this->parseTelegramInfo($order->body);

            // 2. 严格逻辑：只有TG订单才发送群通知，API订单不发送
            if (!$telegramInfo) {
                return true; // API订单跳过群通知
            }

            // 3. 构建支付成功消息
            $message = $this->buildOrderSuccessMessage($order);

            // 4. 获取需要通知的群组
            $chatIds = $this->getNotifyChatIds($order->merchant_id, 'order');

            if (empty($chatIds)) {
                return true; // 没有绑定群组不算失败
            }

            // 5. 发送通知到所有绑定的群组
            $successCount = 0;
            foreach ($chatIds as $chatId) {
                try {
                    // 构建发送选项
                    $options = [];

                    // 如果是原始群组且有消息ID，则回复原始消息
                    if ($telegramInfo &&
                        isset($telegramInfo['chat_id']) &&
                        $telegramInfo['chat_id'] == $chatId &&
                        !empty($telegramInfo['message_id'])) {
                        $options['reply_to_message_id'] = $telegramInfo['message_id'];
                    }

                    $result = $this->telegramService->sendMessage($chatId, $message, $options);
                    if ($result) {
                        $successCount++;
                    }
                } catch (\Exception $e) {
                    \think\facade\Log::error('TG订单通知发送失败', [
                        'order_no' => $order->order_no,
                        'chat_id' => $chatId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // 移除详细完成日志

            return $successCount > 0;

        } catch (\Exception $e) {
            \think\facade\Log::error('TG订单通知异常', [
                'order_no' => $order->order_no,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 构建订单支付成功消息
     *
     * @param \app\common\model\Order $order 订单对象
     * @return string 消息内容
     */
    private function buildOrderSuccessMessage(\app\common\model\Order $order): string
    {

        $message = "✅ <b>支付成功通知</b>\n\n";

        $message .= "• 系统订单号：<code>{$order->order_no}</code>\n";
        $message .= "• 商户订单号：<code>{$order->merchant_order_no}</code>\n";
        $message .= "• 支付金额：<b>{$order->amount} {$order->currency_code}</b>\n";

        $message .= "• 支付时间：" . date('Y-m-d H:i:s', $order->success_time) . "\n";

        if (!empty($order->merchant_name)) {
            $message .= "\n👤 <b>商户信息：</b>\n";
            $message .= "• 商户名称：{$order->merchant_name}\n";
        }

        return $message;
    }

    /**
     * 通知代付订单成功（TG内部调用）
     *
     * @param \app\common\model\DaifuOrder $order 代付订单对象
     * @return bool 是否成功
     */
    public function notifyTransferSuccess(\app\common\model\DaifuOrder $order): bool
    {
        try {
            // 移除过于详细的日志

            // 1. 解析订单中的TG信息，检查是否来自TG
            $telegramInfo = $this->parseTelegramInfo($order->body);

            // 只在body为空且需要临时处理时记录日志
            if (empty($order->body) && $telegramInfo) {
                \think\facade\Log::info('TG代付通知：通过订单号识别为TG订单', [
                    'order_no' => $order->order_no,
                    'merchant_order_no' => $order->merchant_order_no
                ]);
            }

            // 2. 严格逻辑：只有TG订单才发送群通知，API订单不发送
            if (!$telegramInfo) {
                // 临时解决方案：检查订单号是否以TG或TF开头（TG订单的特征）
                $merchantOrderNo = $order->merchant_order_no ?? '';
                if (strpos($merchantOrderNo, 'TG') === 0 || strpos($merchantOrderNo, 'TF') === 0) {
                    // 临时处理：通过订单号识别TG订单
                    // 继续执行通知逻辑，但标记为临时修复
                    $telegramInfo = ['source' => 'telegram_fallback', 'merchant_order_no' => $merchantOrderNo];
                } else {
                    return true; // API订单跳过群通知
                }
            }

            // 3. 构建代付成功消息
            $message = $this->buildTransferSuccessMessage($order);

            // 4. 获取需要通知的群组
            $chatIds = $this->getNotifyChatIds($order->merchant_id, 'daifu');

            if (empty($chatIds)) {
                return true; // 没有绑定群组不算失败
            }

            // 5. 发送通知到所有绑定的群组
            $successCount = 0;
            foreach ($chatIds as $chatId) {
                try {
                    // 构建发送选项
                    $options = [];

                    // 如果是原始群组且有消息ID，则回复原始消息
                    if ($telegramInfo &&
                        isset($telegramInfo['chat_id']) &&
                        $telegramInfo['chat_id'] == $chatId &&
                        !empty($telegramInfo['message_id'])) {
                        $options['reply_to_message_id'] = $telegramInfo['message_id'];

                        // 记录回复设置
                        \think\facade\Log::info('TG代付通知：回复原始消息', [
                            'order_no' => $order->order_no,
                            'reply_to_message_id' => $telegramInfo['message_id']
                        ]);
                    }

                    // 检查TelegramService是否可用
                    if (!$this->telegramService) {
                        \think\facade\Log::error('TG代付通知：TelegramService未初始化', [
                            'order_no' => $order->order_no
                        ]);
                        continue;
                    }

                    $result = $this->telegramService->sendMessage($chatId, $message, $options);

                    // 修正返回值判断逻辑
                    if ($result instanceof \TelegramBot\Api\Types\Message) {
                        $successCount++;
                    } else {
                        \think\facade\Log::warning('TG代付通知：API返回异常结果', [
                            'order_no' => $order->order_no,
                            'chat_id' => $chatId
                        ]);
                    }
                } catch (\Exception $e) {
                    \think\facade\Log::error('TG代付通知发送失败', [
                        'order_no' => $order->order_no,
                        'chat_id' => $chatId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $finalResult = $successCount > 0;

            if (!$finalResult) {
                \think\facade\Log::error('TG代付群组通知全部失败', [
                    'order_no' => $order->order_no,
                    'total_chats' => count($chatIds)
                ]);
            }

            return $finalResult;

        } catch (\Exception $e) {
            \think\facade\Log::error('TG代付通知异常', [
                'order_no' => $order->order_no,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * 强制发送代付成功通知（测试用，绕过来源检查）
     *
     * @param \app\common\model\DaifuOrder $order 代付订单对象
     * @return bool 是否成功
     */
    public function forceNotifyTransferSuccess(\app\common\model\DaifuOrder $order): bool
    {
        try {
            \think\facade\Log::info('强制发送代付成功通知（测试）', [
                'order_no' => $order->order_no,
                'merchant_id' => $order->merchant_id
            ]);

            // 构建代付成功消息
            $message = $this->buildTransferSuccessMessage($order);

            // 获取需要通知的群组
            $chatIds = $this->getNotifyChatIds($order->merchant_id, 'daifu');

            if (empty($chatIds)) {
                \think\facade\Log::warning('强制通知：未找到绑定的群组', [
                    'order_no' => $order->order_no,
                    'merchant_id' => $order->merchant_id
                ]);
                return false;
            }

            // 发送通知到所有绑定的群组
            $successCount = 0;
            foreach ($chatIds as $chatId) {
                try {
                    $result = $this->telegramService->sendMessage($chatId, $message);
                    if ($result) {
                        $successCount++;
                        \think\facade\Log::info('强制通知发送成功', [
                            'order_no' => $order->order_no,
                            'chat_id' => $chatId
                        ]);
                    }
                } catch (\Exception $e) {
                    \think\facade\Log::error('强制通知发送失败', [
                        'order_no' => $order->order_no,
                        'chat_id' => $chatId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            return $successCount > 0;
        } catch (\Exception $e) {
            \think\facade\Log::error('强制代付通知异常', [
                'order_no' => $order->order_no,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 构建代付成功消息
     *
     * @param \app\common\model\DaifuOrder $order 代付订单对象
     * @return string 消息内容
     */
    private function buildTransferSuccessMessage(\app\common\model\DaifuOrder $order): string
    {
        // 解析TG信息，用于显示原始请求信息
        $telegramInfo = $this->parseTelegramInfo($order->body);

        $message = "✅ <b>代付成功通知</b>\n\n";


        $message .= "📋 <b>订单详情：</b>\n";
        $message .= "• 系统订单号：<code>{$order->order_no}</code>\n";
        $message .= "• 商户订单号：<code>{$order->merchant_order_no}</code>\n";
        $message .= "• 代付金额：<b>{$order->amount} {$order->currency_code}</b>\n";

        if (!empty($order->channel_name)) {
            $message .= "• 代付通道：{$order->channel_name}\n";
        }

        $message .= "• 完成时间：" . date('Y-m-d H:i:s', $order->success_time) . "\n";

        if (!empty($order->merchant_name)) {
            $message .= "\n👤 <b>商户信息：</b>\n";
            $message .= "• 商户名称：{$order->merchant_name}\n";
        }

        // 显示收款人信息（优先从TG信息中获取）
        $accountInfo = null;
        if ($telegramInfo && !empty($telegramInfo['account_info'])) {
            $accountInfo = $telegramInfo['account_info'];
        } elseif (!empty($order->extra_params)) {
            $extraParams = is_string($order->extra_params) ? json_decode($order->extra_params, true) : $order->extra_params;
            if (is_array($extraParams)) {
                $accountInfo = $extraParams;
            }
        }

        if ($accountInfo && is_array($accountInfo)) {
            $message .= "\n💳 <b>收款信息：</b>\n";

            if (!empty($accountInfo['account_name'])) {
                $message .= "• 收款人：{$accountInfo['account_name']}\n";
            }

            if (!empty($accountInfo['account_number'])) {
                $message .= "• 收款账号：<code>{$accountInfo['account_number']}</code>\n";
            }
        }

        return $message;
    }

    /**
     * 解析订单中的TG信息
     *
     * @param string $body 订单body字段
     * @return array|null TG信息数组
     */
    private function parseTelegramInfo(?string $body): ?array
    {
        if (empty($body)) {
            return null;
        }

        try {
            $info = json_decode($body, true);
            if (is_array($info) && isset($info['source']) && $info['source'] === 'telegram') {
                // 调试日志：成功解析TG信息
                \think\facade\Log::info('TG信息解析：成功解析', [
                    'chat_id' => $info['chat_id'] ?? 'missing',
                    'message_id' => $info['message_id'] ?? 'missing',
                    'source' => $info['source']
                ]);
                return $info;
            }
        } catch (\Exception $e) {
            // JSON解析失败，忽略
        }

        return null;
    }

    /**
     * 发送代付失败通知到群组
     *
     * @param \app\common\model\DaifuOrder $order 代付订单
     * @param string $reason 失败原因
     * @return bool
     */
    public function notifyTransferFailed(\app\common\model\DaifuOrder $order, string $reason = ''): bool
    {
        try {
            // 1. 解析订单中的TG信息，检查是否来自TG
            $telegramInfo = $this->parseTelegramInfo($order->body);

            // 2. 严格逻辑：只有TG订单才发送群通知，API订单不发送
            if (!$telegramInfo) {
                // 临时解决方案：检查订单号是否以TG或TF开头（TG订单的特征）
                $merchantOrderNo = $order->merchant_order_no ?? '';
                if (strpos($merchantOrderNo, 'TG') === 0 || strpos($merchantOrderNo, 'TF') === 0) {
                    // 尝试从数据库中查找原始的TG信息
                    $telegramInfo = $this->findOriginalTelegramInfo($order);
                    if (!$telegramInfo) {
                        // 如果找不到，创建基本的TG信息
                        $telegramInfo = ['source' => 'telegram_fallback', 'merchant_order_no' => $merchantOrderNo];
                    }
                } else {
                    return true; // API订单跳过群通知
                }
            }

            // 3. 构建代付失败消息
            $message = $this->buildTransferFailedMessage($order, $reason);

            // 4. 获取需要通知的群组
            $chatIds = $this->getNotifyChatIds($order->merchant_id, 'daifu');

            if (empty($chatIds)) {
                return true; // 没有绑定群组不算失败
            }

            // 5. 发送通知到所有绑定的群组
            $successCount = 0;
            foreach ($chatIds as $chatId) {
                try {
                    // 构建发送选项
                    $options = [];

                    // 如果是原始群组且有消息ID，则回复原始消息
                    if ($telegramInfo &&
                        isset($telegramInfo['chat_id']) &&
                        $telegramInfo['chat_id'] == $chatId &&
                        !empty($telegramInfo['message_id'])) {
                        $options['reply_to_message_id'] = $telegramInfo['message_id'];

                        // 记录回复设置
                        \think\facade\Log::info('TG代付失败通知：回复原始消息', [
                            'order_no' => $order->order_no,
                            'reply_to_message_id' => $telegramInfo['message_id']
                        ]);
                    }

                    // 检查TelegramService是否可用
                    if (!$this->telegramService) {
                        \think\facade\Log::error('TG代付失败通知：TelegramService未初始化', [
                            'order_no' => $order->order_no
                        ]);
                        continue;
                    }

                    $result = $this->telegramService->sendMessage($chatId, $message, $options);

                    // 修正返回值判断逻辑
                    if ($result instanceof \TelegramBot\Api\Types\Message) {
                        $successCount++;
                    } else {
                        \think\facade\Log::warning('TG代付失败通知：API返回异常结果', [
                            'order_no' => $order->order_no,
                            'chat_id' => $chatId
                        ]);
                    }
                } catch (\Exception $e) {
                    \think\facade\Log::error('TG代付失败通知发送失败', [
                        'order_no' => $order->order_no,
                        'chat_id' => $chatId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $finalResult = $successCount > 0;

            if (!$finalResult) {
                \think\facade\Log::error('TG代付失败群组通知全部失败', [
                    'order_no' => $order->order_no,
                    'total_chats' => count($chatIds)
                ]);
            }

            return $finalResult;

        } catch (\Exception $e) {
            \think\facade\Log::error('TG代付失败通知异常', [
                'order_no' => $order->order_no,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 构建代付失败消息
     *
     * @param \app\common\model\DaifuOrder $order 代付订单
     * @param string $reason 失败原因
     * @return string
     */
    private function buildTransferFailedMessage(\app\common\model\DaifuOrder $order, string $reason = ''): string
    {
        $amount = $order->amount ?? '0';
        $currency = $order->currency_code ?? '';
        $orderNo = $order->order_no ?? '';
        $merchantOrderNo = $order->merchant_order_no ?? '';

        // 构建失败消息
        $message = "❌ <b>代付失败通知</b>\n\n";
        $message .= "系统订单号: <code>{$orderNo}</code>\n";

        if ($merchantOrderNo) {
            $message .= "商户订单号:<code> {$merchantOrderNo}</code>\n";
        }
        $message .= "代付金额: {$amount} {$currency}\n";
        // 从extra_params中提取收款人信息
        if (!empty($order->extra_params)) {
            $extraParams = null;

            // 处理不同的数据类型
            if (is_string($order->extra_params)) {
                $extraParams = json_decode($order->extra_params, true);
            } elseif (is_array($order->extra_params)) {
                $extraParams = $order->extra_params;
            } elseif (is_object($order->extra_params)) {
                $extraParams = json_decode(json_encode($order->extra_params), true);
            }

            if (is_array($extraParams)) {
                if (!empty($extraParams['account_name'])) {
                    $message .= "👤 收款人: {$extraParams['account_name']}\n";
                }
                if (!empty($extraParams['account_number'])) {
                    $message .= "💳 收款账号: {$extraParams['account_number']}\n";
                }
                if (!empty($extraParams['bank_name'])) {
                    $message .= "🏦 收款银行: {$extraParams['bank_name']}\n";
                }
            }
        }

        // 添加失败原因
        if (!empty($reason)) {
            $message .= "❗ 失败原因: {$reason}\n";
        } elseif (!empty($order->error_msg)) {
            $message .= "❗ 失败原因: {$order->error_msg}\n";
        }

        $message .= "\n⏰ 失败时间: " . date('Y-m-d H:i:s');

        return $message;
    }

    /**
     * 查找原始的TG信息（用于body字段为空的情况）
     *
     * @param \app\common\model\DaifuOrder $order 代付订单
     * @return array|null
     */
    private function findOriginalTelegramInfo(\app\common\model\DaifuOrder $order): ?array
    {
        try {
            // 方法1：通过商户订单号查找BotQuery记录
            $merchantOrderNo = $order->merchant_order_no ?? '';
            if (!empty($merchantOrderNo)) {
                $query = BotQuery::where('query_content', 'like', '%' . $merchantOrderNo . '%')
                    ->where('status', 1)
                    ->order('id', 'desc')
                    ->find();

                if ($query && !empty($query->query_content)) {
                    $queryData = json_decode($query->query_content, true);
                    if (is_array($queryData) && isset($queryData['chat_id']) && isset($queryData['message_id'])) {
                        return [
                            'source' => 'telegram',
                            'chat_id' => $queryData['chat_id'],
                            'message_id' => $queryData['message_id'],
                            'merchant_order_no' => $merchantOrderNo
                        ];
                    }
                }
            }

            // 方法2：通过订单号查找相关的TG记录
            $orderNo = $order->order_no ?? '';
            if (!empty($orderNo)) {
                $query = BotQuery::where('query_content', 'like', '%' . $orderNo . '%')
                    ->where('status', 1)
                    ->order('id', 'desc')
                    ->find();

                if ($query && !empty($query->query_content)) {
                    $queryData = json_decode($query->query_content, true);
                    if (is_array($queryData) && isset($queryData['chat_id']) && isset($queryData['message_id'])) {
                        return [
                            'source' => 'telegram',
                            'chat_id' => $queryData['chat_id'],
                            'message_id' => $queryData['message_id'],
                            'order_no' => $orderNo
                        ];
                    }
                }
            }

            return null;
        } catch (\Exception $e) {
            \think\facade\Log::error('查找原始TG信息失败', [
                'order_no' => $order->order_no,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }


}
