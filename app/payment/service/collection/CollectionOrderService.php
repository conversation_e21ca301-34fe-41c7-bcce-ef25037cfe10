<?php
declare(strict_types=1);

namespace app\payment\service\collection;

use app\common\model\Order;
use app\common\model\Merchant;
use app\common\model\PayChannel;
use app\common\model\PayChannelPool;
use app\common\exception\PaymentException;
use app\common\service\OrderLogService;
use app\common\service\AgentCommissionService;
use app\payment\service\OrderFeeService;
use think\facade\Db;
use think\facade\Cache;

/**
 * 代收订单服务
 */
class CollectionOrderService
{
    // 订单状态常量
    const ORDER_STATUS_WAIT = 0; // 待支付状态
    
    protected $feeService;
    protected $agentCommissionService;

    public function __construct(
        OrderFeeService $feeService,
        AgentCommissionService $agentCommissionService
    ) {
        $this->feeService = $feeService;
        $this->agentCommissionService = $agentCommissionService;
    }

    /**
     * 准备代收订单数据
     * @param Merchant $merchant 商户
     * @param PayChannelPool $channelPool 通道池
     * @param PayChannel $channel 通道
     * @param array $params 请求参数
     * @param string $orderNo 系统订单号
     * @param array|null $poolMember 通道池成员信息
     * @return array
     */
    public function prepareCollectionData(
        Merchant $merchant, 
        PayChannelPool $channelPool, 
        PayChannel $channel, 
        array $params,
        string $orderNo,
        ?array $poolMember = null
    ): array {
        // 记录日志
        // 优化：避免重复查询，使用传入的 poolMember
        if ($poolMember === null) {
            // 设置默认值，避免后续代码出错
            $poolMember = [
                'id' => 0,
                'name' => $channel->name . '_默认成员',
                'weight' => 1,
                'priority' => 1,
                'channel_params' => ''
            ];
        } else {
            // 记录使用传入的通道池成员信息
        }
        
        // 获取币种配置
        $currency = strtoupper($params['currency']);
        
        // 始终通过币种代码查询币种ID，使用缓存方法
        $currencyId = \app\common\model\Currency::getIdByCode($currency);
        if ($currencyId === null) {
            throw new PaymentException("币种{$currency}不存在");
        }
        
        // 计算金额和费率
        $amount = floatval($params['amount']);
        
        // 获取商户费率（优先按通道池查询，如果没有则按具体通道查询）
        $merchantRate = $this->feeService->getMerchantRate($merchant, $channelPool);
        if ($merchantRate == 0) {
            // 如果通道池查询没有结果，尝试按具体通道查询
            $merchantRate = $this->feeService->getMerchantRateByChannel($merchant, $channel);
        }

        // 获取通道费率
        $channelRate = $this->feeService->getChannelRate($channel, $channelPool);



        // 计算所有费用
        $fees = $this->feeService->calculateOrderFees(
            $amount,
            $merchantRate,
            $channelRate
        );
        
        // 如果是测试订单，将所有费用设为0
        if (!empty($params['is_test']) && $params['is_test'] == 1) {
            $fees['merchant_fee'] = 0;
            $fees['merchant_amount'] = $amount; // 测试订单全额到账
            $fees['channel_cost'] = 0;
            $fees['platform_profit'] = 0;

            // 测试订单费用设为0
        }

        // 记录通道池成员信息
        // 构建订单数据
        $orderData = [
            'tenant_id' => $params['tenant_id'] ?? 0,
            'order_no' => $orderNo,
            'merchant_id' => $merchant->id,
            'merchant_name' => $merchant->username,
            'merchant_order_no' => $params['merchant_order_no'],
            'channel_id' => $channel->id,
            'channel_code' => $channel->code,
            'channel_name' => $channel->name,
            'type_id' => $channelPool->id,
            'type_name' => $channelPool->name,
            'type_code' => $channelPool->code,
            'pay_type_id' => $channelPool->id,
            'pay_type_name' => $channelPool->name,
            'amount' => $amount,
            'fee' => $fees['merchant_fee'],
            //'channel_cost' => $fees['channel_cost'],
            //'platform_profit' => $fees['platform_profit'],
            'rate' => $merchantRate,
            'channel_rate' => $channelRate,
            'real_amount' => $amount,
            'received_amount' => $fees['merchant_amount'], // 到账金额 = 订单金额 - 手续费
            'currency_id' => $currencyId,
            'currency_code' => $currency,
            'subject' => $params['subject'] ?? '',
            'body' => $params['body'] ?? '',
            'account_id' => $poolMember['id'] ?? 0,
            'account_name' => $poolMember['name'] ?? '',
            'account_params' => $poolMember['channel_params'] ?? '',
            'pool_member_id' => $poolMember['id'] ?? 0, // 添加通道池成员ID
            'pool_member_name' => $poolMember['name'] ?? '', // 添加通道池成员名称
            'pool_member_weight' => $poolMember['weight'] ?? 0, // 添加权重信息
            'pool_member_priority' => $poolMember['priority'] ?? 0, // 添加优先级信息
            'notify_url' => $params['notify_url'],
            'return_url' => $params['return_url'] ?? '',
            'notify_status' => 0,
            'settle_status' => 0,
            'notify_times' => 0,
            'last_notify_time' => 0,
            'client_ip' => request()->ip(),
            'pay_time' => 0,
            'expire_time' => 10, // 10分钟有效期（存储分钟数）
            'complete_time' => 0,
            'status' => 0, // 待支付
            'create_time' => time(),
            'is_test' => $params['is_test'] ?? 0, // 是否测试订单：0=正式订单，1=测试订单
            'update_time' => time(),
            'site_id' => $params['site_id'] ?? 0,
            'agent_site_id' => $params['agent_site_id'] ?? 0,
            'direction' => 1, // 代收
        ];
        
        return $orderData;
    }
    
    /**
     * 创建代收订单
     */
    public function createCollectionWithData(array $orderData): Order
    {
        // 创建订单模型
        $order = new Order();
        $order->save($orderData);
        
        if (!$order->isExists()) {
            throw new PaymentException('订单创建失败');
        }
        
        // 添加订单到过期队列
        $this->addOrderToExpireQueue($order);
        
        return $order;
    }
    
    /**
     * 添加订单到过期队列（优化版）
     */
    protected function addOrderToExpireQueue(Order $order): bool
    {
        // 测试订单不加入过期队列
        if ($order->is_test == 1) {
            return true;
        }

        try {
            $redis = Cache::store('redis')->handler();

            // 使用管道批量操作，提高性能
            $pipe = $redis->multi();

            // 添加到有序集合（按过期时间排序）
            // 智能计算过期时间戳：兼容时间戳和分钟数两种格式
            if ($order->expire_time > 1000000000) {
                // expire_time是时间戳，直接使用
                $expireTimestamp = $order->expire_time;
            } else {
                // expire_time是分钟数，转换为时间戳
                $expireTimestamp = $order->create_time + ($order->expire_time * 60);
            }
            $pipe->zadd('order_expire_queue', $expireTimestamp, $order->id);

            // 添加订单详情到哈希表（便于快速查询）
            $pipe->hset('order_expire_details', $order->id, json_encode([
                'order_no' => $order->order_no,
                'merchant_id' => $order->merchant_id,
                'amount' => $order->amount,
                'expire_time' => $expireTimestamp, // 使用计算后的时间戳
                'direction' => $order->direction ?? 1
            ]));

            // 设置过期时间（防止内存泄漏）
            $pipe->expire('order_expire_details', 86400 * 7); // 7天过期

            $results = $pipe->exec();

            return $results[0] !== false;
        } catch (\Exception $e) {            return false;
        }
    }
    
    /**
     * 更新代收订单状态
     */
    public function updateCollectionStatus(Order $order, int $status, string $remark = '', int $notifyType = 4): void
    {
        // 检查状态是否已经更新
        if ($order->status == $status) {            return;
        }
        
        // 更新订单状态
        $order->status = $status;
        $order->remark = $remark;

        // 如果是成功状态，设置支付时间、完成时间和回调类型
        if ($status == Order::STATUS_PAID) {
            $order->pay_time = time();
            $order->complete_time = time();
            $order->notify_type = $notifyType; // 设置回调类型（默认为上游回调）

            // 从过期队列中移除
            $this->removeOrderFromExpireQueue((int)$order->id);

            // 更新统计数据
            $this->updateChannelStats($order->channel_id, $order->amount);
        }
        
        // 如果是失败、过期、未出码状态，设置完成时间
        if (Order::isFailedStatus($status)) {
            $order->complete_time = time();

            // 从过期队列中移除
            $this->removeOrderFromExpireQueue((int)$order->id);
        }
        
        // 保存订单
        $order->save();
        
        // 记录日志
    }
    
    /**
     * 从过期队列中移除订单（优化版）
     */
    protected function removeOrderFromExpireQueue(int $orderId): bool
    {
        try {
            $redis = Cache::store('redis')->handler();

            // 使用管道批量操作
            $pipe = $redis->multi();
            $pipe->zrem('order_expire_queue', $orderId);
            $pipe->hdel('order_expire_details', $orderId);
            $results = $pipe->exec();

            return $results[0] > 0; // 返回是否真正移除了元素
        } catch (\Exception $e) {            return false;
        }
    }
    
    /**
     * 更新通道统计数据
     */
    protected function updateChannelStats(int $channelId, float $amount): void
    {
        $statDate = date('Y-m-d');
        
        // 使用数据库事务
        Db::startTrans();
        try {
            // 查询当天统计记录
            $stat = Db::name('pay_channel_stat')
                ->where('channel_id', $channelId)
                ->where('stat_date', $statDate)
                ->lock(true)
                ->find();
                
            if ($stat) {
                // 更新现有记录
                Db::name('pay_channel_stat')
                    ->where('id', $stat['id'])
                    ->update([
                        'success_count' => Db::raw('success_count + 1'),
                        'success_amount' => Db::raw('success_amount + ' . $amount),
                        'total_count' => Db::raw('total_count + 1'),
                        'total_amount' => Db::raw('total_amount + ' . $amount),
                        'update_time' => time()
                    ]);
            } else {
                // 创建新记录
                Db::name('pay_channel_stat')->insert([
                    'channel_id' => $channelId,
                    'success_count' => 1,
                    'success_amount' => $amount,
                    'fail_count' => 0,
                    'total_count' => 1,
                    'total_amount' => $amount,
                    'stat_date' => $statDate,
                    'create_time' => time(),
                    'update_time' => time()
                ]);
            }
            
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();        }
    }
} 