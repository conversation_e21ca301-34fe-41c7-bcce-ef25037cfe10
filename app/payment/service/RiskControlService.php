<?php
declare(strict_types=1);

namespace app\payment\service;

use app\common\model\Merchant;
use app\common\model\MerchantChannel;
use app\common\model\PayChannel;
use app\common\model\PayChannelRisk;
use app\common\model\Order;
use app\common\model\PayChannelPool;
use app\common\exception\PaymentException;
use app\common\service\PayChannelPoolService;
use think\Collection;
use think\facade\Cache;
use think\facade\Db;
use app\common\service\OrderLogService;
use app\payment\service\PaymentDataService;

/**
 * 风控服务
 * 
 * 注意：系统已统一使用通道池机制进行通道分配，
 * 不再使用直接通道关联机制（channel_ids、weight_config、channel_params）
 */
class RiskControlService
{
    /**
     * 交易方向常量
     */
    public const DIRECTION_COLLECTION = 1; // 代收
    public const DIRECTION_PAYMENT = 2;    // 代付
    
    /**
     * 多通道风控配置缓存键
     */
    private const MULTI_CHANNEL_RISK_CACHE_KEY = 'payment:multi_channel_risk:%d';
    
    /**
     * 订单类型 - 代收
     */
    public const TYPE_COLLECTION = 1;
    
    /**
     * 订单类型 - 代付
     */
    public const TYPE_PAYMENT = 2;
    
    /**
     * @var PaymentDataService
     */
    protected PaymentDataService $paymentDataService;
    
    /**
     * @var PayChannelPoolService
     */
    protected ?PayChannelPoolService $channelPoolService;
    
    public function __construct(
        PaymentDataService $paymentDataService,
        PayChannelPoolService $channelPoolService = null
    ) {
        $this->paymentDataService = $paymentDataService;
        $this->channelPoolService = $channelPoolService ?? app(PayChannelPoolService::class, [], true);
    }
    
    /**
     * 获取多通道风控配置
     */
    protected function getMultiChannelRiskConfig($channelPool): array
    {
        if (!$channelPool) {
            return [
                'is_multi_channel' => false,
                'channel_id' => 0,
                'dispatch_mode' => 1
            ];
        }
        
        return [
            'is_multi_channel' => true,
            'pool_id' => $channelPool->id,
            'dispatch_mode' => $channelPool->dispatch_mode ?? 1
        ];
    }

    /**
     * 分配支付账号
     */
    public function assignPaymentAccount(
        Merchant $merchant, 
        MerchantChannel $merchantChannel, 
        PayChannel $channel,
        $channelPool,
        float $amount,
        string $orderNo = '',
        int $direction = 1  // 默认代收
    ): array {
        // 获取风控配置
        $risk = $this->getChannelRisk($channel->id);
        
        // 获取通道参数
        $channelParams = $this->parseChannelParams($channel->params);
        
        // 检查是否使用多通道风控
        $multiChannelConfig = $this->getMultiChannelRiskConfig($channelPool);
        
        // 如果是多通道模式，使用通道池服务分配通道
        if ($multiChannelConfig['is_multi_channel'] && $multiChannelConfig['pool_id'] > 0) {
            try {
                list($selectedChannel, $selectedParams) = $this->channelPoolService->getAvailableChannel(
                    $multiChannelConfig['pool_id'],
                    $amount,
                    $multiChannelConfig['dispatch_mode'] == 2 ? 'weight' : 'round_robin',
                    $direction
                );
                
                // 更新通道信息
                $channel = $selectedChannel;
                $channelParams = $selectedParams;
                
                // 记录选择的通道
            } catch (\Exception $e) {
                // 如果通道池分配失败，使用原始通道
            }
        }
        
        // 获取支付数据类型（从新的配置结构中获取）
        $payDataType = 1; // 默认为链接类型
        if ($risk && !empty($risk->collection_config)) {
            $collectionConfig = is_string($risk->collection_config) ? json_decode($risk->collection_config, true) : $risk->collection_config;
            $payDataType = $collectionConfig['pay_data_type'] ?? 1;
        }
        
        // 生成支付账号
        $paymentAccount = $this->generatePaymentAccount($channel, $risk, $channelParams);
        
        // 记录日志
        OrderLogService::addLog($orderNo, '分配支付账号', [
            'channel_id' => $channel->id,
            'channel_name' => $channel->name,
            'account_id' => $paymentAccount['id'] ?? 0,
            'account_name' => $paymentAccount['name'] ?? '',
            'pay_data_type' => $payDataType,
            'pay_data_type_name' => $this->paymentDataService->getPaymentDataTypeName($payDataType)
        ]);
        
        return [
            'channel' => $channel,
            'risk' => $risk,
            'account' => $paymentAccount,
            'pay_data_type' => $payDataType
        ];
    }
    
    /**
     * 代收账号分配（使用通道池服务）
     */
    private function assignCollectionAccount(
        Merchant $merchant, 
        MerchantChannel $merchantChannel, 
        PayChannel $channel,
        PayChannelPool $channelPool,
        float $amount,
        string $orderNo = ''
    ): array {
        // 记录参数用于调试
        $tenantId = $merchant->site_id ?? 0;
        $logContent = "---------------------[派单检测]开始---------------------\n";
        $logContent .= "[派单检测]租户ID[{$tenantId}],订单号[{$orderNo}],订单金额[" . number_format($amount, 2) . "],通道编码[{$channel->code}],通道名称[{$channel->name}]\n";
        
        // 使用Redis分布式锁防止并发分配
        $redis = Cache::store('redis')->handler();
        $lockKey = "payment:assign_lock:{$channel->id}:" . intval($amount * 100);
        $lockValue = uniqid('lock_', true);
        
        // 缩短锁时间为2秒
        $acquireLock = $redis->set($lockKey, $lockValue, ['NX', 'EX' => 2]); 
        
        if (!$acquireLock) {            // 快速失败，避免阻塞 - 等待50ms后重试一次
            usleep(50000); // 50毫秒
            $acquireLock = $redis->set($lockKey, $lockValue, ['NX', 'EX' => 2]);
            
            if (!$acquireLock) {
                throw new PaymentException('系统繁忙，请稍后再试 [E1]');
            }
        }
        
        try {
            // 检查是否配置了通道池
            $poolId = $channelPool->id ?? 0;
            
            if ($poolId > 0) {
                $logContent .= "[派单检测][通道池模式]使用通道池ID[{$poolId}]\n";
                
                try {
                    // 先释放锁，轮询逻辑有自己的锁保护
                    $this->releaseLock($redis, $lockKey, $lockValue);
                    
                    // 使用通道池服务获取适合的通道和参数
                    if ($this->channelPoolService) {
                        $dispatchMode = $channelPool->dispatch_mode == 2 ? 'weight' : 'round_robin';
                        
                        // 获取可用通道
                        list($selectedChannel, $channelParams) = $this->channelPoolService->getAvailableChannel(
                            $poolId, 
                            $amount, 
                            $dispatchMode
                        );
                        
                        $logContent .= "[派单检测][通道池结果]选择通道ID[{$selectedChannel->id}]通道名称[{$selectedChannel->name}]\n";
                        
                        $logContent .= "---------------------[派单检测]结束---------------------\n";                        return [
                            'selected_channel' => $selectedChannel,
                            'channel_params' => $channelParams
                        ];
                    } else {
                        // 如果通道池服务不可用，使用默认通道
                        return [
                            'selected_channel' => $channel,
                            'channel_params' => $this->getChannelParams($channel)
                        ];
                    }
                } catch (\Exception $e) {
                    // 如果通道池分配失败，使用默认通道
                }
            }
            
            // 如果没有通道池，使用原始通道
            $logContent .= "[派单检测][原始通道]使用直接配置的通道\n";
            
            // 获取通道参数
            $channelParams = $this->getChannelParams($channel);
            
            $logContent .= "---------------------[派单检测]结束---------------------\n";            // 确保释放锁
            $this->releaseLock($redis, $lockKey, $lockValue);
            
            return [
                'selected_channel' => $channel,
                'channel_params' => $channelParams
            ];
        } catch (\Exception $e) {
            // 确保异常情况下也会释放锁
            $this->releaseLock($redis, $lockKey, $lockValue);
            throw $e;
        }
    }
    
    /**
     * 安全释放Redis锁
     * 
     * @param \Predis\Client $redis Redis客户端
     * @param string $lockKey 锁键名
     * @param string $lockValue 锁值
     * @return bool 是否成功释放锁
     */
    private function releaseLock($redis, string $lockKey, string $lockValue): bool
    {
        $script = <<<LUA
        if redis.call('GET', KEYS[1]) == ARGV[1] then
            return redis.call('DEL', KEYS[1])
        else
            return 0
        end
        LUA;
        
        return (bool)$redis->eval($script, [$lockKey], [$lockValue]);
    }
    
    /**
     * 获取可用通道列表
     */
    protected function getAvailableChannels(PayChannelPool $channelPool, float $amount): array
    {
        $poolId = $channelPool->id ?? 0;
        
        // 如果配置了通道池，从通道池获取通道
        if ($poolId > 0) {
            $poolMembers = \app\common\model\PayChannelPoolMember::where('pool_id', $poolId)
                ->where('status', 1)
                ->select();
                
            if ($poolMembers->isEmpty()) {
                throw new PaymentException('通道池没有可用通道');
            }
            
            // 获取通道ID列表
            $channelIds = $poolMembers->column('channel_id');
        } else {
            // 如果没有配置通道池，则使用默认通道ID
            $channelIds = [$channelPool->default_channel_id ?? 0];
        }
        
        // 1. 尝试从缓存获取
        $cached = $this->getMultiChannelCache($channelPool->id, $amount);
        if ($cached !== null) {
            return $cached;
        }

        try {
            $availableChannels = [];
            
            // 2. 获取多通道配置
            $multiChannelConfig = $this->getMultiChannelRiskConfig($channelPool);
            
            // 3. 遍历检查每个通道
            foreach ($multiChannelConfig['channel_codes'] as $channelCode) {
                $channel = PayChannel::where('code', $channelCode)->find();
                if (!$channel) {
                    $this->logRiskControl('通道不存在', [
                        'channel_code' => $channelCode
                    ]);
                continue;
            }
            
                $riskConfig = $multiChannelConfig['risk_configs'][$channelCode] ?? [];
                
                // 检查通道风控规则
                if ($this->checkChannelRiskRules($channel, $amount, $riskConfig)) {
                    $availableChannels[] = [
                        'channel' => $channel,
                        'risk_config' => $riskConfig
                    ];
                }
            }

            // 4. 缓存结果
            $this->setMultiChannelCache($channelPool->id, $amount, $availableChannels);

            return $availableChannels;
        } catch (\Exception $e) {
            $this->logRiskControl('获取可用通道异常', [
                'pay_type_id' => $channelPool->id,
                'amount' => $amount,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 轮询通道策略
     * 
     * @param array $channelIds 通道ID列表
     * @param float $amount 订单金额 
     * @param int $merchantId 商户ID，用于指定轮询键
     * @return PayChannel 选定的通道
     * @throws PaymentException 如果没有可用通道
     */
    protected function roundRobinChannelStrategy(array $channelIds, float $amount, int $merchantId = 0): PayChannel
    {
        if (empty($channelIds)) {
            throw new PaymentException('没有可用通道ID列表');
        }

        // 记录日志        // 过滤出有效的通道
        $availableChannels = [];
        foreach ($channelIds as $channelId) {
            try {
                $channel = PayChannel::where([
                    ['id', '=', $channelId],
                    ['status', '=', 1]
                ])->find();
                
                if ($channel) {
                    // 检查通道限额
                    if (isset($channel->min_amount) && $channel->min_amount > 0 && $amount < $channel->min_amount) {                continue;
            }
            
                    if (isset($channel->max_amount) && $channel->max_amount > 0 && $amount > $channel->max_amount) {                continue;
            }
            
                    $availableChannels[] = $channel;
                }
            } catch (\Exception $e) {            }
        }
        
        if (empty($availableChannels)) {
            throw new PaymentException('没有可用的支付通道');
        }
        
        // 1. 从缓存获取当前轮询位置，使用特定键（可根据商户ID区分）
        $redis = Cache::store('redis')->handler();
        $roundRobinKey = "payment:channel_round_robin:" . ($merchantId > 0 ? $merchantId : 'global');
        $currentIndex = (int)$redis->get($roundRobinKey);
        
        // 2. 如果超出范围，重置为0
        if ($currentIndex >= count($availableChannels)) {
            $currentIndex = 0;
        }
        
        // 3. 选择当前位置的通道
        $selectedChannel = $availableChannels[$currentIndex];
        
        // 4. 更新下一个位置
        $nextIndex = ($currentIndex + 1) % count($availableChannels);
        $redis->setex($roundRobinKey, 86400, $nextIndex);        return $selectedChannel;
    }
    
    /**
     * 获取通道参数
     */
    private function getChannelParams(PayChannel $channel): array
    {
        $params = is_string($channel->params) ? 
            json_decode($channel->params, true) : $channel->params;
        
        return is_array($params) ? $params : [];
    }
    
    /**
     * 处理通道参数
     */
    private function processChannelParams(PayChannel $channel): array
    {
        return $this->getChannelParams($channel);
    }

    /**
     * 获取可用码商列表
     */
    protected function getAvailableCodeMerchants(int $payTypeId, int $appointedCodeMerchantId = 0): Collection
    {
        // 此方法已废弃，返回空集合
        return new Collection([]);
    }
    
    /**
     * 根据条件筛选码商
     */
    protected function filterCodeMerchantsByCondition(Collection $codeMerchants, float $amount, int $payTypeId): array
    {
        // 此方法已废弃，返回空数组
        return [];
    }
    
    /**
     * 选择码商 - 轮询策略
     */
    protected function selectCodeMerchant(array $codeMerchants, int $payTypeId = 0)
    {
        // 此方法已废弃，返回null
            return null;
    }

    /**
     * 处理支付返回结果
     */
    public function processPaymentResult(array $result, ?PayChannelRisk $risk, Order $order): array
    {
        return $this->paymentDataService->processPaymentResult($result, $risk, $order);
    }
    
    /**
     * 检查余额
     */
    protected function checkBalance(float $balance, float $amount): void
    {
        if ($balance < $amount) {
            throw new PaymentException('账号余额不足');
        }    }
    
    /**
     * 风控检查 - 代收（支持通道池继承机制）
     */
    public function checkCollectionRisk(
        Merchant $merchant,
        MerchantChannel $merchantChannel,
        PayChannel $channel,
        float $amount,
        string $orderNo = '',
        int $memberId = 0,
        int $poolId = 0
    ): array {
        $this->logRiskControl('开始代收风控检查', [
            'merchant_id' => $merchant->id,
            'channel_id' => $channel->id,
            'member_id' => $memberId,
            'pool_id' => $poolId,
            'amount' => $amount,
            'order_no' => $orderNo
        ]);

        // 1. 获取有效的风控配置（支持继承机制）
        $risk = $this->getEffectiveRiskConfig($memberId, $poolId, $channel->id, PayChannelRisk::DIRECTION_COLLECTION);

        $this->logRiskControl('获取风控配置', [
            'member_id' => $memberId,
            'pool_id' => $poolId,
            'channel_id' => $channel->id,
            'risk_config_found' => $risk ? true : false,
            'inherit_mode' => $risk ? $risk->inherit_mode : null
        ]);

        // 2. 检查基础风控规则
        $basicCheck = $this->checkBasicRiskRules($merchant, $channel, $amount, $risk, $poolId);
        if (!$basicCheck['pass']) {
            return $basicCheck;
        }

        // 3. 检查商户风控规则
        $merchantCheck = $this->checkMerchantRiskRules($merchant, $amount, $orderNo);
        if (!$merchantCheck['pass']) {
            return $merchantCheck;
        }

        // 4. 检查通道特定风控规则
        $channelCheck = $this->checkChannelSpecificRules($channel, $amount, $risk, $memberId);
        if (!$channelCheck['pass']) {
            return $channelCheck;
        }

        $this->logRiskControl('代收风控检查通过', [
            'merchant_id' => $merchant->id,
            'channel_id' => $channel->id,
            'member_id' => $memberId,
            'pool_id' => $poolId,
            'amount' => $amount,
            'order_no' => $orderNo
        ]);

        return [
            'pass' => true,
            'message' => '风控检查通过',
            'level' => 0
        ];
    }

    /**
     * 风控检查 - 代付（支持通道池继承机制）
     */
    public function checkPaymentRisk(
        Merchant $merchant,
        MerchantChannel $merchantChannel,
        PayChannel $channel,
        float $amount,
        string $orderNo = '',
        array $params = [],
        int $memberId = 0,
        int $poolId = 0
    ): array {
        try {
            $this->logRiskControl('开始代付风控检查', [
                'merchant_id' => $merchant->id,
                'channel_id' => $channel->id,
                'member_id' => $memberId,
                'pool_id' => $poolId,
                'amount' => $amount,
                'order_no' => $orderNo
            ]);

            // 1. 获取有效的风控配置（支持继承机制）
            $risk = $this->getEffectiveRiskConfig($memberId, $poolId, $channel->id, PayChannelRisk::DIRECTION_PAYMENT);

            $this->logRiskControl('获取代付风控配置', [
                'member_id' => $memberId,
                'pool_id' => $poolId,
                'channel_id' => $channel->id,
                'risk_config_found' => $risk ? true : false,
                'inherit_mode' => $risk ? $risk->inherit_mode : null
            ]);

            // 2. 基础风控检查
            $basicCheck = $this->checkBasicTransferRisk($merchant, $amount, $params, $risk, $merchantChannel, $channel);
            if (!$basicCheck['pass']) {
                return $basicCheck;
            }

            // 3. 通道风控检查
            $channelCheck = $this->checkChannelTransferRisk($channel, $amount, $params, $risk);
            if (!$channelCheck['pass']) {
                return $channelCheck;
            }

            // 4. 商户通道风控检查
            $merchantChannelCheck = $this->checkMerchantChannelTransferRisk($merchantChannel, $amount, $params);
            if (!$merchantChannelCheck['pass']) {
                return $merchantChannelCheck;
            }

            // 5. 时间段风控检查
            $timeCheck = $this->checkTransferTimeRisk($channel, $params, $risk);
            if (!$timeCheck['pass']) {
                return $timeCheck;
            }

            $this->logRiskControl('代付风控检查通过', [
                'merchant_id' => $merchant->id,
                'channel_id' => $channel->id,
                'member_id' => $memberId,
                'pool_id' => $poolId,
                'amount' => $amount,
                'order_no' => $orderNo
            ]);

            return [
                'pass' => true,
                'message' => '风控检查通过',
                'level' => 0
            ];

        } catch (\Exception $e) {
            $this->logRiskControl('代付风控检查异常', [
                'merchant_id' => $merchant->id,
                'channel_id' => $channel->id,
                'member_id' => $memberId,
                'pool_id' => $poolId,
                'error' => $e->getMessage()
            ]);

            return [
                'pass' => false,
                'message' => '风控检查异常：' . $e->getMessage(),
                'level' => 3
            ];
        }
    }

    /**
     * 基础代付风控检查
     */
    protected function checkBasicTransferRisk(
        Merchant $merchant,
        float $amount,
        array $params,
        ?PayChannelRisk $risk = null,
        ?MerchantChannel $merchantChannel = null,
        ?PayChannel $channel = null
    ): array
    {
        // 1. 检查商户状态
        if ($merchant->status != 1) {
            return [
                'pass' => false,
                'message' => '商户状态异常，无法进行代付',
                'level' => 3
            ];
        }

        // 2. 检查代付功能是否开启
        if (empty($merchant->df_status) || $merchant->df_status != 1) {
            return [
                'pass' => false,
                'message' => '商户代付功能未开启',
                'level' => 3
            ];
        }

        // 3. 检查单笔限额
        if (!empty($merchant->transfer_min_amount) && $amount < $merchant->transfer_min_amount) {
            return [
                'pass' => false,
                'message' => '代付金额低于商户最小限额',
                'level' => 2
            ];
        }

        if (!empty($merchant->transfer_max_amount) && $amount > $merchant->transfer_max_amount) {
            return [
                'pass' => false,
                'message' => '代付金额超过商户最大限额',
                'level' => 2
            ];
        }

        // 4. 检查日限额（如果配置了）
        if (!empty($merchant->transfer_day_limit)) {
            $todayAmount = $this->getTodayTransferAmount($merchant->id);
            if (($todayAmount + $amount) > $merchant->transfer_day_limit) {
                return [
                    'pass' => false,
                    'message' => '超过商户日代付限额',
                    'level' => 2
                ];
            }
        }

        // 5. 检查商户通道金额限制（代付）
        if ($merchantChannel && $channel) {
            $channelLimitCheck = $this->checkMerchantChannelAmountLimit($merchant, $channel, $amount, $merchantChannel, $risk);
            if (!$channelLimitCheck['pass']) {
                return $channelLimitCheck;
            }
        }

        return [
            'pass' => true,
            'message' => '基础风控检查通过',
            'level' => 0
        ];
    }

    /**
     * 通道代付风控检查
     */
    protected function checkChannelTransferRisk(PayChannel $channel, float $amount, array $params, ?PayChannelRisk $risk = null): array
    {
        // 1. 检查通道状态
        if ($channel->status != 1) {
            return [
                'pass' => false,
                'message' => '支付通道已关闭',
                'level' => 3
            ];
        }

        // 2. 检查通道是否支持代付
        if ($channel->direction != 2 && $channel->direction != 3) { // 2=代付, 3=代收+代付
            return [
                'pass' => false,
                'message' => '通道不支持代付功能',
                'level' => 3
            ];
        }

        // 3. 检查通道限额
        if (!empty($channel->min_amount) && $amount < $channel->min_amount) {
            return [
                'pass' => false,
                'message' => '代付金额低于通道最小限额',
                'level' => 2
            ];
        }

        if (!empty($channel->max_amount) && $amount > $channel->max_amount) {
            return [
                'pass' => false,
                'message' => '代付金额超过通道最大限额',
                'level' => 2
            ];
        }

        return [
            'pass' => true,
            'message' => '通道风控检查通过',
            'level' => 0
        ];
    }

    /**
     * 商户通道代付风控检查
     */
    protected function checkMerchantChannelTransferRisk(MerchantChannel $merchantChannel, float $amount, array $params): array
    {
        // 1. 检查商户通道状态
        if ($merchantChannel->status != 1) {
            return [
                'pass' => false,
                'message' => '商户通道已关闭',
                'level' => 3
            ];
        }

//        // 2. 检查商户通道是否支持代付
//        if (empty($merchantChannel->transfer_status) || $merchantChannel->transfer_status != 1) {
//            return [
//                'pass' => false,
//                'message' => '商户通道代付功能未开启',
//                'level' => 3
//            ];
//        }

        return [
            'pass' => true,
            'message' => '商户通道风控检查通过',
            'level' => 0
        ];
    }

    /**
     * 代付时间段风控检查
     */
    protected function checkTransferTimeRisk(PayChannel $channel, array $params, ?PayChannelRisk $risk = null): array
    {
        // 检查通道是否配置了时间限制
        if (!empty($channel->work_time_start) && !empty($channel->work_time_end)) {
            $currentTime = date('H:i:s');

            if ($currentTime < $channel->work_time_start || $currentTime > $channel->work_time_end) {
                return [
                    'pass' => false,
                    'message' => '当前时间不在通道工作时间范围内',
                    'level' => 2
                ];
            }
        }

        return [
            'pass' => true,
            'message' => '时间段风控检查通过',
            'level' => 0
        ];
    }

    /**
     * 获取商户今日代付金额
     */
    protected function getTodayTransferAmount(int $merchantId): float
    {
        $today = date('Y-m-d');
        $startTime = strtotime($today . ' 00:00:00');
        $endTime = strtotime($today . ' 23:59:59');

        $amount = \app\common\model\DaifuOrder::where('merchant_id', $merchantId)
            ->where('status', \app\common\model\DaifuOrder::STATUS_SUCCESS)
            ->where('create_time', '>=', $startTime)
            ->where('create_time', '<=', $endTime)
            ->sum('amount');

        return (float)$amount;
    }

    /**
     * 获取通道风控配置
     */
    public function getChannelRisk(int $channelId, int $direction = PayChannelRisk::DIRECTION_COLLECTION): ?PayChannelRisk
    {
        return PayChannelRisk::getByChannel($channelId, $direction);
    }

    /**
     * 获取有效的风控配置（支持通道池成员优先级）
     */
    public function getEffectiveRiskConfig(int $memberId = 0, int $poolId = 0, int $channelId = 0, int $direction = PayChannelRisk::DIRECTION_COLLECTION): ?PayChannelRisk
    {
        return PayChannelRisk::getEffectiveConfig($memberId, $poolId, $channelId, $direction);
    }

    /**
     * 风控日志记录
     */
    protected function logRiskControl(string $message, array $context = []): void
    {
        // 日志已禁用
    }

    /**
     * 检查基础风控规则
     */
    protected function checkBasicRiskRules(Merchant $merchant, PayChannel $channel, float $amount, ?PayChannelRisk $risk, int $poolId = 0): array
    {
        // 1. 检查通道状态
        if ($channel->status != 1) {
            return [
                'pass' => false,
                'message' => '通道状态不可用',
                'level' => 3
            ];
        }

        // 2. 检查商户状态
        if ($merchant->status != 1) {
            return [
                'pass' => false,
                'message' => '商户状态异常',
                'level' => 3
            ];
        }

        // 3. 检查商户通道金额限制
        $channelLimitCheck = $this->checkMerchantChannelAmountLimit($merchant, $channel, $amount, null, $risk, $poolId);

        if (!$channelLimitCheck['pass']) {
            return $channelLimitCheck;
        }

        return ['pass' => true];
    }

    /**
     * 检查商户通道金额限制（商户通道优先，风控配置降级）
     */
    protected function checkMerchantChannelAmountLimit(
        Merchant $merchant,
        PayChannel $channel,
        float $amount,
        ?MerchantChannel $merchantChannel = null,
        ?PayChannelRisk $risk = null,
        int $poolId = 0
    ): array {
        // 日志已禁用

        $minAmount = 0;
        $maxAmount = 0;

        // 如果没有传入商户通道，尝试查询（使用通道池ID）
        if (!$merchantChannel && $poolId > 0) {
            // 暂时禁用缓存，直接查询数据库
            $merchantChannel = MerchantChannel::where([
                'merchant_id' => $merchant->id,
                'channel_id' => $poolId,  // 注意：这里的channel_id实际是通道池ID
                'status' => 1
            ])->find();
        }

        // 首先尝试从商户通道配置获取限制
        if ($merchantChannel) {
            $minAmount = $merchantChannel->min_amount ?? 0;
            $maxAmount = $merchantChannel->max_amount ?? 0;
        }

        // 如果商户通道没有设置限制，使用风控配置
        if ($minAmount <= 0 && $maxAmount <= 0 && $risk) {
            $minAmount = $risk->min_amount ?? 0;
            $maxAmount = $risk->max_amount ?? 0;
        }

        // 检查最小金额限制
        if ($minAmount > 0 && $amount < $minAmount) {
            $source = $merchantChannel ? '商户通道' : '风控配置';
            return [
                'pass' => false,
                'message' => "订单金额{$amount}小于{$source}最小限额{$minAmount}",
                'level' => 2
            ];
        }

        // 检查最大金额限制
        if ($maxAmount > 0 && $amount > $maxAmount) {
            $source = $merchantChannel ? '商户通道' : '风控配置';
            return [
                'pass' => false,
                'message' => "订单金额{$amount}超过{$source}最大限额{$maxAmount}",
                'level' => 2
            ];
        }

        return ['pass' => true];
    }

    /**
     * 清除商户通道限制缓存
     */
    public function clearMerchantChannelCache(int $merchantId, int $poolId): void
    {
        $cacheKey = "merchant_channel_limit_{$merchantId}_{$poolId}";
        Cache::delete($cacheKey);
    }

    /**
     * 检查商户风控规则
     */
    protected function checkMerchantRiskRules(Merchant $merchant, float $amount, string $orderNo): array
    {
        // 1. 检查商户余额（如果需要预扣费）
        // 这里可以根据业务需求添加商户余额检查

        // 2. 检查商户日限额
        $todayAmount = $this->getMerchantTodayAmount($merchant->id);
        $dailyLimit = $merchant->daily_limit ?? 0;

        if ($dailyLimit > 0 && ($todayAmount + $amount) > $dailyLimit) {
            return [
                'pass' => false,
                'message' => '超过商户日限额',
                'level' => 2
            ];
        }

        // 3. 检查商户频率限制
        $recentOrderCount = $this->getMerchantRecentOrderCount($merchant->id, 60); // 1分钟内订单数
        $maxOrdersPerMinute = 10; // 每分钟最多10笔订单

        if ($recentOrderCount >= $maxOrdersPerMinute) {
            return [
                'pass' => false,
                'message' => '订单频率过高',
                'level' => 1
            ];
        }

        return ['pass' => true];
    }

    /**
     * 检查通道特定风控规则
     */
    protected function checkChannelSpecificRules(PayChannel $channel, float $amount, ?PayChannelRisk $risk, int $memberId = 0): array
    {
        if (!$risk) {
            return ['pass' => true];
        }

        // 1. 检查通道日限额
        $todayAmount = $this->getChannelTodayAmount($channel->id, $memberId);
        $dailyLimit = $risk->daily_limit ?? 0;

        if ($dailyLimit > 0 && ($todayAmount + $amount) > $dailyLimit) {
            return [
                'pass' => false,
                'message' => '超过通道日限额',
                'level' => 2
            ];
        }

        // 2. 检查通道时间限制
        if (!$this->checkChannelTimeLimit($risk)) {
            return [
                'pass' => false,
                'message' => '当前时间不在通道允许时间范围内',
                'level' => 1
            ];
        }

        return ['pass' => true];
    }

    /**
     * 获取商户今日交易金额
     */
    protected function getMerchantTodayAmount(int $merchantId): float
    {
        $today = date('Y-m-d');
        $startTime = strtotime($today . ' 00:00:00');
        $endTime = strtotime($today . ' 23:59:59');

        $amount = Db::name('order')
            ->where('merchant_id', $merchantId)
            ->where('status', 2) // 已支付
            ->where('pay_time', '>=', $startTime)
            ->where('pay_time', '<=', $endTime)
            ->sum('amount');

        return (float)$amount;
    }

    /**
     * 获取商户最近订单数量
     */
    protected function getMerchantRecentOrderCount(int $merchantId, int $seconds): int
    {
        $startTime = time() - $seconds;

        return Db::name('order')
            ->where('merchant_id', $merchantId)
            ->where('create_time', '>=', $startTime)
            ->count();
    }

    /**
     * 获取通道今日交易金额（兼容新旧表结构）
     */
    protected function getChannelTodayAmount(int $channelId, int $memberId = 0): float
    {
        $today = date('Y-m-d');
        $startTime = strtotime($today . ' 00:00:00');
        $endTime = strtotime($today . ' 23:59:59');

        // 优先使用 member_id 查询（如果有的话）
        if ($memberId > 0) {
            try {
                $amount = Order::where('member_id', $memberId)
                    ->where('status', 2) // 已支付
                    ->where('pay_time', '>=', $startTime)
                    ->where('pay_time', '<=', $endTime)
                    ->sum('amount');
                return (float)$amount;
            } catch (\Exception $e) {
                // member_id 字段不存在，使用 channel_id
            }
        }

        // 使用 channel_id 查询
        try {
            $amount = Order::where('channel_id', $channelId)
                ->where('status', 2) // 已支付
                ->where('pay_time', '>=', $startTime)
                ->where('pay_time', '<=', $endTime)
                ->sum('amount');
            return (float)$amount;
        } catch (\Exception $e) {
            // 如果查询失败，记录错误并返回0
            $this->logRiskControl('获取通道今日交易金额失败', [
                'channel_id' => $channelId,
                'member_id' => $memberId,
                'error' => $e->getMessage()
            ]);
            return 0.0;
        }
    }

    /**
     * 检查通道时间限制
     */
    protected function checkChannelTimeLimit(?PayChannelRisk $risk): bool
    {
        if (!$risk) {
            return true; // 没有风控配置
        }

        // 检查 allowed_hours 字段（新的时间限制字段）
        if (!empty($risk->allowed_hours)) {
            $allowedHours = is_string($risk->allowed_hours) ? json_decode($risk->allowed_hours, true) : $risk->allowed_hours;
            if ($allowedHours && is_array($allowedHours)) {
                $currentTime = date('H:i');
                $startTime = $allowedHours['start_time'] ?? '00:00';
                $endTime = $allowedHours['end_time'] ?? '23:59';

                return $currentTime >= $startTime && $currentTime <= $endTime;
            }
        }

        // 兼容旧的 time_limit 字段
        if (!empty($risk->time_limit)) {
            $timeLimit = json_decode($risk->time_limit, true);
            if ($timeLimit) {
                $currentTime = date('H:i');
                $startTime = $timeLimit['start_time'] ?? '00:00';
                $endTime = $timeLimit['end_time'] ?? '23:59';

                return $currentTime >= $startTime && $currentTime <= $endTime;
            }
        }

        return true; // 没有时间限制
    }

    /**
     * 多通道缓存键
     */
    private const MULTI_CHANNEL_CACHE_KEY = 'payment:multi_channel:%d:%d';

    /**
     * 获取多通道缓存
     */
    protected function getMultiChannelCache(int $payTypeId, float $amount): ?array
    {
        $cacheKey = sprintf(self::MULTI_CHANNEL_CACHE_KEY, $payTypeId, (int)($amount * 100));
        return Cache::get($cacheKey);
    }

    /**
     * 设置多通道缓存
     */
    protected function setMultiChannelCache(int $payTypeId, float $amount, array $data, int $ttl = 300): void
    {
        $cacheKey = sprintf(self::MULTI_CHANNEL_CACHE_KEY, $payTypeId, (int)($amount * 100));
        Cache::set($cacheKey, $data, $ttl);
    }

    /**
     * 清除多通道缓存
     */
    protected function clearMultiChannelCache(int $payTypeId, float $amount): void
    {
        $cacheKey = sprintf(self::MULTI_CHANNEL_CACHE_KEY, $payTypeId, (int)($amount * 100));
        Cache::delete($cacheKey);
    }

    /**
     * 检查通道风控规则
     */
    protected function checkChannelRiskRules(PayChannel $channel, float $amount, array $riskConfig): bool
    {
        try {
            // 1. 检查通道状态
            if ($channel->status != 1) {
                $this->logRiskControl('通道状态不可用', [
                    'channel_id' => $channel->id,
                    'channel_code' => $channel->code
                ]);
                return false;
            }

            // 2. 检查金额限制
            if (isset($riskConfig['min_amount']) && $riskConfig['min_amount'] > 0 && $amount < $riskConfig['min_amount']) {
                $this->logRiskControl('订单金额小于通道最小限额', [
                    'channel_id' => $channel->id,
                    'amount' => $amount,
                    'min_amount' => $riskConfig['min_amount']
                ]);
                return false;
            }

            if (isset($riskConfig['max_amount']) && $riskConfig['max_amount'] > 0 && $amount > $riskConfig['max_amount']) {
                $this->logRiskControl('订单金额超过通道最大限额', [
                    'channel_id' => $channel->id,
                    'amount' => $amount,
                    'max_amount' => $riskConfig['max_amount']
                ]);
                return false;
            }

            // 3. 检查日限额
            if (isset($riskConfig['day_limit']) && $riskConfig['day_limit'] > 0) {
                $today = date('Y-m-d');
                $todayAmount = 0;

                // 优先使用 member_id 查询（如果有的话）
                if ($memberId > 0) {
                    try {
                        $todayAmount = Order::whereRaw("DATE_FORMAT(create_time,'%Y-%m-%d') = ?", [$today])
                            ->where('status', '>=', 0)
                            ->where('member_id', $memberId)
                            ->sum('amount');
                    } catch (\Exception $e) {
                        // member_id 字段不存在，使用 channel_id
                        $todayAmount = Order::whereRaw("DATE_FORMAT(create_time,'%Y-%m-%d') = ?", [$today])
                            ->where('status', '>=', 0)
                            ->where('channel_id', $channel->id)
                            ->sum('amount');
                    }
                } else {
                    // 直接使用 channel_id 查询
                    $todayAmount = Order::whereRaw("DATE_FORMAT(create_time,'%Y-%m-%d') = ?", [$today])
                        ->where('status', '>=', 0)
                        ->where('channel_id', $channel->id)
                        ->sum('amount');
                }

                if ($todayAmount + $amount > $riskConfig['day_limit']) {
                    $this->logRiskControl('通道当日限额不足', [
                        'channel_id' => $channel->id,
                        'member_id' => $memberId ?? 0,
                        'today_amount' => $todayAmount,
                        'day_limit' => $riskConfig['day_limit']
                    ]);
                    return false;
                }
            }

            // 4. 检查日限单
            if (isset($riskConfig['day_order_limit']) && $riskConfig['day_order_limit'] > 0) {
                $today = date('Y-m-d');
                $todayOrderCount = 0;

                // 优先使用 member_id 查询（如果有的话）
                if ($memberId > 0) {
                    try {
                        $todayOrderCount = Order::whereRaw("DATE_FORMAT(create_time,'%Y-%m-%d') = ?", [$today])
                            ->where('status', '>=', 0)
                            ->where('member_id', $memberId)
                            ->count();
                    } catch (\Exception $e) {
                        // member_id 字段不存在，使用 channel_id
                        $todayOrderCount = Order::whereRaw("DATE_FORMAT(create_time,'%Y-%m-%d') = ?", [$today])
                            ->where('status', '>=', 0)
                            ->where('channel_id', $channel->id)
                            ->count();
                    }
                } else {
                    // 直接使用 channel_id 查询
                    $todayOrderCount = Order::whereRaw("DATE_FORMAT(create_time,'%Y-%m-%d') = ?", [$today])
                        ->where('status', '>=', 0)
                        ->where('channel_id', $channel->id)
                        ->count();
                }

                if ($todayOrderCount >= $riskConfig['day_order_limit']) {
                    $this->logRiskControl('通道当日限单已满', [
                        'channel_id' => $channel->id,
                        'member_id' => $memberId ?? 0,
                        'today_order_count' => $todayOrderCount,
                        'day_order_limit' => $riskConfig['day_order_limit']
                    ]);
                    return false;
                }
            }

            // 5. 检查时间限制
            if (isset($riskConfig['time_limit']) && $riskConfig['time_limit'] == 1) {
                $currentHour = (int)date('H');
                $startHour = $riskConfig['start_hour'] ?? 0;
                $endHour = $riskConfig['end_hour'] ?? 24;

                if ($currentHour < $startHour || $currentHour >= $endHour) {
                    $this->logRiskControl('通道不在可用时间范围内', [
                        'channel_id' => $channel->id,
                        'current_hour' => $currentHour,
                        'start_hour' => $startHour,
                        'end_hour' => $endHour
                    ]);
                    return false;
                }
            }

            return true;
        } catch (\Exception $e) {
            $this->logRiskControl('检查通道风控规则异常', [
                'channel_id' => $channel->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取多通道参数设置
     * 
     * @param PayChannelPool $channelPool 通道池对象
     * @param int $channelId 通道ID
     * @return array 参数设置
     */
    public function getMultiChannelParams(PayChannelPool $channelPool, int $channelId): array
    {
        // 检查通道池是否配置了通道参数覆盖
        if (empty($channelPool->id) || $channelId <= 0) {
            return [];
        }
        
        // 查询通道池成员配置
        $poolMember = \app\common\model\PayChannelPoolMember::where([
            'pool_id' => $channelPool->id,
            'channel_id' => $channelId,
            'status' => 1
        ])->find();
        
        if (!$poolMember) {
            return [];
        }
        
        // 解析通道参数
        $channelParams = $poolMember->channel_params;
        if (is_string($channelParams)) {
            $channelParams = json_decode($channelParams, true);
        }
        
        return is_array($channelParams) ? $channelParams : [];
    }
} 