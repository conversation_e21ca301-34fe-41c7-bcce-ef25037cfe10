<?php
declare(strict_types=1);

namespace app\common\service;

use TelegramBot\Api\BotApi;
use TelegramBot\Api\Types\Message;
use TelegramBot\Api\Exception;
use GuzzleHttp\Client;

class TelegramBotService extends BaseService
{
    /**
     * Telegram Bot API 实例
     *
     * @var BotApi
     */
    protected $telegram;

    /**
     * 当前机器人Token
     *
     * @var string
     */
    protected $currentToken;

    /**
     * 构造函数
     *
     * @param string|null $token Bot API Token
     * @throws Exception
     */
    public function __construct(?string $token = null)
    {
        $token = $token ?: config('telegram.token');

        if (empty($token)) {
            throw new Exception('Telegram Bot Token 未配置');
        }

        $this->initializeBot($token);
    }

    /**
     * 初始化机器人实例
     *
     * @param string $token Bot API Token
     * @throws Exception
     */
    protected function initializeBot(string $token): void
    {
        $this->currentToken = $token;
        $api_url = config('telegram.api_url', 'https://api.telegram.org/bot');

        // 临时禁用SSL验证以解决本地开发环境的SSL问题
        $this->disableSSLVerification();

        // 初始化 Telegram Bot API，直接传入自定义 URL
        $this->telegram = new BotApi($token, $api_url);

        // 为 TelegramBot\Api 设置 cURL 选项
        if (method_exists($this->telegram, 'setCurlOption')) {
            $this->telegram->setCurlOption(CURLOPT_SSL_VERIFYPEER, false);
            $this->telegram->setCurlOption(CURLOPT_SSL_VERIFYHOST, false);
        }
    }

    /**
     * 设置新的Token并重新初始化机器人
     *
     * @param string $token 新的Bot API Token
     * @throws Exception
     */
    public function setToken(string $token): void
    {
        if ($this->currentToken !== $token) {
            $this->initializeBot($token);
        }
    }

    /**
     * 获取当前Token
     *
     * @return string
     */
    public function getCurrentToken(): string
    {
        return $this->currentToken;
    }

    /**
     * 临时禁用SSL验证（仅用于开发环境）
     */
    private function disableSSLVerification()
    {
        // 设置 PHP 的默认 SSL 上下文
        // 设置默认流上下文选项
        $contextOptions = [
            "ssl" => [
                "verify_peer" => false,
                "verify_peer_name" => false,
                "allow_self_signed" => true,
            ],
            "http" => [
                "timeout" => 30,
            ]
        ];
        stream_context_set_default($contextOptions);

        // 设置 cURL 默认选项
        if (function_exists('curl_setopt_array')) {
            // 这个方法可能不会影响到TelegramBot\Api库内部的cURL调用
            // 但我们先尝试
        }
    }

    /**
     * 发送消息
     *
     * @param int $chatId 聊天ID
     * @param string $text 消息内容
     * @param array $options 额外选项
     * @return \TelegramBot\Api\Types\Message
     * @throws Exception
     */
    public function sendMessage(int $chatId, string $text, array $options = [])
    {
        $params = array_merge([
            'chat_id' => $chatId,
            'text' => $text,
            'parse_mode' => 'HTML'
        ], $options);

        return $this->telegram->sendMessage(
            $params['chat_id'],
            $params['text'],
            $params['parse_mode'] ?? null,
            $params['disable_web_page_preview'] ?? null,
            $params['reply_to_message_id'] ?? null,
            $params['reply_markup'] ?? null,
            $params['disable_notification'] ?? null
        );
    }

    /**
     * 发送带按钮的消息
     *
     * @param int $chatId 聊天ID
     * @param string $text 消息内容
     * @param array $buttons 按钮数组
     * @param array $options 额外选项
     * @return \TelegramBot\Api\Types\Message
     * @throws Exception
     */
    public function sendMessageWithButtons(int $chatId, string $text, array $buttons = [], array $options = [])
    {
        $replyMarkup = null;
        if (!empty($buttons)) {
            $replyMarkup = new \TelegramBot\Api\Types\Inline\InlineKeyboardMarkup($buttons);
        }

        return $this->telegram->sendMessage(
            $chatId,
            $text,
            'HTML',
            $options['disable_web_page_preview'] ?? null,
            $options['reply_to_message_id'] ?? null,
            $replyMarkup,
            $options['disable_notification'] ?? null
        );
    }

    /**
     * 发送文档
     * @param int $chatId 聊天ID
     * @param string $filePath 文件路径
     * @param array $options 额外选项
     * @return \TelegramBot\Api\Types\Message
     * @throws Exception
     */
    public function sendDocument(int $chatId, string $filePath, array $options = [])
    {
        if (!file_exists($filePath)) {
            throw new \Exception("文件不存在: {$filePath}");
        }

        $document = new \CURLFile($filePath);

        // 传递正确的参数，按照telegram-bot/api库的参数顺序
        return $this->telegram->sendDocument(
            $chatId,
            $document,
            $options['caption'] ?? null,           // 第3个参数：caption
            $options['reply_to_message_id'] ?? null, // 第4个参数：replyToMessageId
            null  // 第5个参数：reply_markup
        );
    }

    /**
     * 编辑消息
     *
     * @param int $chatId 聊天ID
     * @param int $messageId 消息ID
     * @param string $text 新的消息内容
     * @param array $buttons 按钮数组
     * @return \TelegramBot\Api\Types\Message
     * @throws Exception
     */
    public function editMessage(int $chatId, int $messageId, string $text, array $buttons = [])
    {
        $replyMarkup = null;
        if (!empty($buttons)) {
            $replyMarkup = new \TelegramBot\Api\Types\Inline\InlineKeyboardMarkup($buttons);
        }

        return $this->telegram->editMessageText(
            $chatId,
            $messageId,
            $text,
            'HTML',
            null,
            $replyMarkup
        );
    }

    /**
     * 删除消息
     *
     * @param int $chatId 聊天ID
     * @param int $messageId 消息ID
     * @return bool
     * @throws Exception
     */
    public function deleteMessage(int $chatId, int $messageId): bool
    {
        return $this->telegram->deleteMessage($chatId, $messageId);
    }

    /**
     * 获取机器人信息
     *
     * @return \TelegramBot\Api\Types\User
     * @throws Exception
     */
    public function getMe()
    {
        return $this->telegram->getMe();
    }

    /**
     * 设置 Webhook
     *
     * @param string $url Webhook URL
     * @return bool
     * @throws Exception
     */
    public function setWebhook(string $url): bool
    {
        return $this->telegram->setWebhook($url);
    }

    /**
     * 删除 Webhook
     *
     * @return bool
     * @throws Exception
     */
    public function deleteWebhook(): bool
    {
        return $this->telegram->deleteWebhook();
    }

    /**
     * 获取 Webhook 信息
     *
     * @return \TelegramBot\Api\Types\WebhookInfo
     * @throws Exception
     */
    public function getWebhookInfo()
    {
        return $this->telegram->getWebhookInfo();
    }

    /**
     * 获取 API 实例
     *
     * @return BotApi
     */
    public function getApi(): BotApi
    {
        return $this->telegram;
    }

    /**
     * 获取机器人用户名
     *
     * @return string|null
     * @throws Exception
     */
    public function getBotUsername(): ?string
    {
        try {
            $me = $this->getMe();
            return $me->getUsername();
        } catch (\Exception $e) {
            throw new Exception('获取机器人信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试机器人Token是否有效
     *
     * @param string|null $token 要测试的Token，为空则测试当前Token
     * @return array|false 成功返回机器人信息，失败返回false
     */
    public function testBotToken(?string $token = null): array|false
    {
        try {
            $originalToken = $this->currentToken;

            if ($token && $token !== $this->currentToken) {
                $this->setToken($token);
            }

            $me = $this->getMe();
            $result = [
                'id' => $me->getId(),
                'username' => $me->getUsername(),
                'first_name' => $me->getFirstName(),
                'is_bot' => $me->isBot(),
            ];

            // 恢复原Token
            if ($token && $token !== $originalToken) {
                $this->setToken($originalToken);
            }

            return $result;
        } catch (\Exception $e) {
            // 恢复原Token
            if ($token && isset($originalToken)) {
                try {
                    $this->setToken($originalToken);
                } catch (\Exception $restoreException) {
                    // 忽略恢复失败
                }
            }
            return false;
        }
    }





    /**
     * 发送照片
     *
     * @param string|int $chat_id 聊天ID
     * @param string $photo 照片URL或文件ID
     * @param array $options 其他选项
     * @return Message 响应数据
     * @throws Exception
     */
    public function sendPhoto($chat_id, string $photo, array $options = []): Message
    {
        $caption = $options['caption'] ?? null;
        $parse_mode = $options['parse_mode'] ?? null;
        $disable_notification = $options['disable_notification'] ?? null;
        $reply_to_message_id = $options['reply_to_message_id'] ?? null;
        $reply_markup = $options['reply_markup'] ?? null;
        
        return $this->telegram->sendPhoto(
            $chat_id,
            $photo,
            $caption,
            $reply_to_message_id,
            $reply_markup,
            $disable_notification,
            $parse_mode
        );
    }

    /**
     * 发送带按钮的图片消息
     *
     * @param int $chatId 聊天ID
     * @param string $photo 照片URL或文件ID
     * @param string $caption 图片说明
     * @param array $buttons 按钮数组
     * @param array $options 其他选项
     * @return Message 响应数据
     * @throws Exception
     */
    public function sendPhotoWithButtons(int $chatId, string $photo, string $caption = '', array $buttons = [], array $options = []): Message
    {
        $replyMarkup = null;
        if (!empty($buttons)) {
            $replyMarkup = new \TelegramBot\Api\Types\Inline\InlineKeyboardMarkup($buttons);
        }

        $parse_mode = $options['parse_mode'] ?? 'HTML';
        $disable_notification = $options['disable_notification'] ?? null;
        $reply_to_message_id = $options['reply_to_message_id'] ?? null;

        return $this->telegram->sendPhoto(
            $chatId,
            $photo,
            $caption,
            $reply_to_message_id,
            $replyMarkup,
            $disable_notification,
            $parse_mode
        );
    }

    /**
     * 回答回调查询
     *
     * @param string $callbackQueryId 回调查询ID
     * @param string $text 显示的文本
     * @param bool $showAlert 是否显示警告
     * @return bool
     * @throws Exception
     */
    public function answerCallbackQuery(string $callbackQueryId, string $text = '', bool $showAlert = false): bool
    {
        return $this->telegram->answerCallbackQuery($callbackQueryId, $text, $showAlert);
    }

    /**
     * 获取机器人更新信息
     *
     * @param array $options 选项
     * @return array 更新数据
     * @throws Exception
     */
    public function getUpdates(array $options = []): array
    {
        $offset = $options['offset'] ?? null;
        $limit = $options['limit'] ?? null;
        $timeout = $options['timeout'] ?? null;

        return $this->telegram->getUpdates($offset, $limit, $timeout);
    }




} 