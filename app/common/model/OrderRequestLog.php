<?php
declare(strict_types=1);

namespace app\common\model;

use think\Model;

/**
 * 商户请求日志模型
 */
class OrderRequestLog extends Model
{
    // 设置表名
    protected $name = 'order_request_log';
    
    // 自动写入时间戳
    protected $autoWriteTimestamp = true;
    
    // 字段类型
    protected $type = [
        'id' => 'integer',
        'merchant_id' => 'string',
        'merchant_order_no' => 'string',
        'response_code' => 'integer',
        'process_time' => 'integer',
        'status' => 'integer',
        'create_time' => 'integer',
        'update_time' => 'integer',
    ];
    
    /**
     * 与商户表关联
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class, 'merchant_id', 'id');
    }

    /**
     * 与订单表关联
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_no', 'order_no');
    }
    
    /**
     * 状态获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            0 => '失败',
            1 => '成功',
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 快速创建日志记录
     * 
     * @param string $merchantId 商户ID
     * @param string $merchantName 商户名称
     * @param string $MerchantOrderNo 商户订单号
     * @param string $apiName API名称
     * @param string $requestUrl 请求URL
     * @param string $requestMethod 请求方法
     * @param array $requestHeaders 请求头信息
     * @param array $requestParams 请求参数
     * @param string $requestIp 请求IP
     * @param string $requestId 请求ID
     * @return OrderRequestLog
     */
    public static function record(
        string $merchantId,
        string $merchantName,
        string $MerchantOrderNo,
        string $apiName,
        string $requestUrl,
        string $requestMethod,
        array $requestHeaders = [],
        array $requestParams = [],
        string $requestIp = '',
        string $requestId = ''
    ): self {
        // 过滤敏感信息
//        if (isset($requestParams['sign'])) {
//            $requestParams['sign'] = '******';
//        }
        
        // 过滤敏感头信息
        if (isset($requestHeaders['Authorization'])) {
            $requestHeaders['Authorization'] = '******';
        }
        
        // 创建记录
        return self::create([
            'merchant_id' => $merchantId,
            'merchant_name' => $merchantName,
            'merchant_order_no' => $MerchantOrderNo,
            'request_id' => $requestId ?: uniqid('req_', true),
            'api_name' => $apiName,
            'request_url' => $requestUrl,
            'request_method' => $requestMethod,
            'request_headers' => json_encode($requestHeaders, JSON_UNESCAPED_UNICODE),
            'request_params' => json_encode($requestParams, JSON_UNESCAPED_UNICODE),
            'request_ip' => $requestIp,
            'status' => 1, // 默认成功
            'create_time' => time(),
            'update_time' => time()
        ]);
    }
    
    /**
     * 更新响应信息
     * 
     * @param int $responseCode 响应状态码
     * @param array|string $responseData 响应数据
     * @param int $processTime 处理时间(毫秒)
     * @param bool $isSuccess 是否成功
     * @param string $errorMessage 错误信息
     * @return bool
     */
    public function updateResponse(
        int $responseCode,
        $responseData,
        int $processTime = 0,
        bool $isSuccess = true,
        string $errorMessage = ''
    ): bool {
        // 如果响应数据是数组，转为JSON
        if (is_array($responseData)) {
            $responseData = json_encode($responseData, JSON_UNESCAPED_UNICODE);
        }

        // 使用响应限制器限制长度
        $responseData = \app\common\service\ResponseLimiter::limitResponse($responseData);
        $errorMessage = \app\common\service\ResponseLimiter::limitErrorMessage($errorMessage);

        // 更新记录
        return $this->save([
            'response_code' => $responseCode,
            'response_data' => $responseData,
            'process_time' => $processTime,
            'status' => $isSuccess ? 1 : 0,
            'error_message' => $errorMessage,
            'update_time' => time()
        ]);
    }

    /**
     * 根据订单号获取请求日志
     * 
     * @param string $orderNo 订单号
     * @return OrderRequestLog|null
     */
    public static function getByOrderNo(string $orderNo): ?self
    {
        return self::where('merchant_order_no', $orderNo)
            ->order('create_time', 'desc')
            ->find();
    }

    /**
     * 根据订单号获取所有请求日志
     * 
     * @param string $orderNo 订单号
     * @return array
     */
    public static function getAllByOrderNo(string $orderNo): array
    {
        return self::where('merchant_order_no', $orderNo)
            ->order('create_time', 'desc')
            ->select()
            ->toArray();
    }
} 