# 性能优化环境变量配置模板
# 复制此文件为 .env 并根据实际环境调整配置

# ================================
# 基础应用配置
# ================================
APP_DEBUG=false
APP_TRACE=false

# ================================
# 性能监控配置
# ================================
# 是否启用性能监控
PERFORMANCE_MONITORING=true

# 性能监控采样率（1-100，生产环境建议 5-10）
PERFORMANCE_SAMPLE_RATE=10

# 慢请求阈值（毫秒）
SLOW_REQUEST_THRESHOLD=200

# 是否启用性能调试信息（生产环境建议 false）
PERFORMANCE_DEBUG=false

# 性能日志级别（debug, info, warning, error）
PERFORMANCE_LOG_LEVEL=warning

# 是否启用性能分析（生产环境建议 false）
PERFORMANCE_PROFILING=false

# 性能分析采样率（1-100，启用时建议 1-5）
PERFORMANCE_PROFILING_RATE=1

# ================================
# 缓存优化配置
# ================================
# 是否启用缓存预热
CACHE_WARMUP_ENABLE=true

# 缓存预热间隔（分钟）
CACHE_WARMUP_INTERVAL=360

# 用户缓存 TTL（秒）
USER_CACHE_TTL=300

# 用户缓存最大条目数
USER_CACHE_MAX_ENTRIES=10000

# 是否启用用户缓存压缩
USER_CACHE_COMPRESSION=false

# 配置缓存 TTL（秒）
CONFIG_CACHE_TTL=3600

# 是否启用配置缓存预加载
CONFIG_CACHE_PRELOAD=true

# OAuth2 缓存 TTL（秒）
OAUTH_CACHE_TTL=1800

# 是否启用 OAuth2 分布式缓存
OAUTH_CACHE_DISTRIBUTED=false

# 缓存预热最大用户数（每种类型）
CACHE_WARMUP_MAX_USERS=100

# 缓存预热并发数
CACHE_WARMUP_CONCURRENT=5

# ================================
# 数据库优化配置
# ================================
# 是否启用数据库连接池
DB_POOL_ENABLE=true

# 数据库连接池最小连接数
DB_POOL_MIN=5

# 数据库连接池最大连接数
DB_POOL_MAX=20

# 数据库连接空闲超时（秒）
DB_POOL_IDLE_TIMEOUT=300

# 是否启用数据库查询缓存（高并发下建议 false）
DB_QUERY_CACHE=false

# 是否记录慢查询日志
DB_SLOW_QUERY_LOG=true

# 慢查询阈值（毫秒）
DB_SLOW_QUERY_THRESHOLD=100

# 数据库批量操作大小
DB_BATCH_SIZE=100

# 是否启用批量事务
DB_BATCH_TRANSACTION=true

# 事务超时时间（秒）
DB_TRANSACTION_TIMEOUT=30

# ================================
# 队列配置
# ================================
# 是否启用异步日志记录
ASYNC_LOGGING_ENABLE=true

# 异步日志批量大小
ASYNC_LOG_BATCH_SIZE=10

# 是否启用登录日志队列
LOGIN_LOG_QUEUE_ENABLE=true

# 登录日志队列名称
LOGIN_LOG_QUEUE_NAME=login_log

# 登录日志最大重试次数
LOGIN_LOG_MAX_ATTEMPTS=3

# 登录日志重试延迟（秒）
LOGIN_LOG_RETRY_DELAY=30

# 是否启用缓存预热队列
CACHE_WARMUP_QUEUE_ENABLE=false

# 缓存预热队列名称
CACHE_WARMUP_QUEUE_NAME=cache_warmup

# 缓存预热并发任务数
CACHE_WARMUP_CONCURRENT_JOBS=3

# ================================
# 内存监控配置
# ================================
# 是否启用内存监控
MEMORY_MONITORING=true

# 内存使用告警阈值（MB）
MEMORY_WARNING_THRESHOLD=512

# ================================
# 告警配置
# ================================
# 是否启用性能告警
PERFORMANCE_ALERTS_ENABLE=false

# 告警邮箱地址
PERFORMANCE_ALERT_EMAIL=<EMAIL>

# 告警 Webhook URL
PERFORMANCE_ALERT_WEBHOOK=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# ================================
# Redis 配置（如果使用 Redis 缓存）
# ================================
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SELECT=0

# ================================
# 数据库配置
# ================================
DATABASE_HOST=127.0.0.1
DATABASE_PORT=3306
DATABASE_NAME=your_database
DATABASE_USERNAME=your_username
DATABASE_PASSWORD=your_password

# ================================
# 生产环境特殊配置
# ================================
# 是否为生产环境
APP_ENV=production

# 错误报告级别
ERROR_REPORTING=E_ERROR

# 是否显示错误详情
SHOW_ERROR_MSG=false

# 日志级别
LOG_LEVEL=error

# ================================
# 安全配置
# ================================
# JWT 密钥（请修改为随机字符串）
JWT_SECRET=your-secret-key-change-this

# OAuth2 加密密钥（请修改为随机字符串）
OAUTH_ENCRYPTION_KEY=your-oauth-encryption-key

# ================================
# 其他优化配置
# ================================
# 是否启用 OPcache（推荐启用）
OPCACHE_ENABLE=true

# 是否启用文件缓存
FILE_CACHE_ENABLE=true

# 静态资源版本号（用于缓存控制）
STATIC_VERSION=1.0.0

# ================================
# 监控端点配置
# ================================
# 健康检查端点是否需要认证
HEALTH_CHECK_AUTH=false

# 性能监控端点是否需要认证
PERFORMANCE_MONITOR_AUTH=true

# ================================
# 备注说明
# ================================
# 1. 生产环境请务必修改所有密钥和密码
# 2. 根据服务器配置调整连接池和缓存大小
# 3. 监控告警功能需要配置相应的邮件或 Webhook 服务
# 4. 队列功能需要启动 queue:listen 进程
# 5. 定时任务需要配置 crontab
# 6. 建议使用 Redis 作为缓存驱动以获得最佳性能
