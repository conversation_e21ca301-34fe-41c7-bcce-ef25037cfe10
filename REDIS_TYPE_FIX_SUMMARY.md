# Redis 类型错误修复总结

## 问题描述

遇到 Redis 类型错误：
```
Redis::zRangeByScore(): Argument #2 ($start) must be of type string, int given
```

这个错误是因为 Redis PHP 扩展的 `zrangebyscore`、`zcount` 等方法要求分数参数必须是字符串类型，但代码中传入了整数类型。

## 修复的文件

### 1. app/command/job/handlers/OrderSingleExpireHandler.php

**修复位置：第23行**
```php
// 修复前
$expiredOrderIds = $redis->zrangebyscore('order_expire_queue', 0, $currentTime, [
    'limit' => [0, $batchSize]
]);

// 修复后
$expiredOrderIds = $redis->zrangebyscore('order_expire_queue', '0', (string)$currentTime, [
    'limit' => [0, $batchSize]
]);
```

### 2. app/command/DiagnoseOrderExpire.php

**修复位置：第105、109、113行**
```php
// 修复前
$expiredCount = $redis->zcount('order_expire_queue', 0, $currentTime);
$futureCount = $redis->zcount('order_expire_queue', $currentTime + 1, '+inf');
$recentExpired = $redis->zrangebyscore('order_expire_queue', 0, $currentTime, [
    'withscores' => true,
    'limit' => [0, 5]
]);

// 修复后
$expiredCount = $redis->zcount('order_expire_queue', '0', (string)$currentTime);
$futureCount = $redis->zcount('order_expire_queue', (string)($currentTime + 1), '+inf');
$recentExpired = $redis->zrangebyscore('order_expire_queue', '0', (string)$currentTime, [
    'withscores' => true,
    'limit' => [0, 5]
]);
```

### 3. app/command/FixOrderExpireQueue.php

**修复位置：第274行**
```php
// 修复前
$expiredCount = $redis->zcount('order_expire_queue', 0, $currentTime);

// 修复后
$expiredCount = $redis->zcount('order_expire_queue', '0', (string)$currentTime);
```

## 修复原则

### 需要字符串类型的 Redis 方法参数

以下 Redis 方法的分数参数必须是字符串类型：

1. **zrangebyscore($key, $min, $max, $options)**
   - `$min` 和 `$max` 必须是字符串
   - 示例：`'0'`, `'100'`, `'+inf'`, `'-inf'`

2. **zrevrangebyscore($key, $max, $min, $options)**
   - `$max` 和 `$min` 必须是字符串

3. **zcount($key, $min, $max)**
   - `$min` 和 `$max` 必须是字符串

4. **zremrangebyscore($key, $min, $max)**
   - `$min` 和 `$max` 必须是字符串

### 类型转换方法

```php
// 方法1：使用 (string) 强制转换
$redis->zcount('key', '0', (string)$currentTime);

// 方法2：使用字符串字面量
$redis->zcount('key', '0', "$currentTime");

// 方法3：对于特殊值，直接使用字符串
$redis->zcount('key', '-inf', '+inf');
```

## 验证修复

修复后，以下操作应该正常工作：

1. **订单过期处理器**：能够正常获取和处理过期订单
2. **诊断命令**：能够正确统计和显示过期订单信息
3. **修复命令**：能够正确检查队列状态

## 注意事项

1. **一致性**：确保所有使用这些 Redis 方法的地方都使用字符串参数
2. **性能**：字符串转换的性能开销很小，不会影响系统性能
3. **兼容性**：这个修复与所有版本的 Redis PHP 扩展兼容
4. **测试**：建议在生产环境部署前进行充分测试

## 相关 Redis 命令

这些命令在 Redis 中都需要字符串类型的分数参数：
- ZRANGEBYSCORE
- ZREVRANGEBYSCORE  
- ZCOUNT
- ZREMRANGEBYSCORE
- ZLEXCOUNT (字典序相关)

## 预防措施

为了避免类似问题，建议：

1. **代码审查**：在代码审查时特别注意 Redis 分数参数的类型
2. **静态分析**：使用 PHPStan 等工具检查类型问题
3. **单元测试**：为 Redis 相关功能编写单元测试
4. **文档**：在团队文档中明确 Redis 参数类型要求
