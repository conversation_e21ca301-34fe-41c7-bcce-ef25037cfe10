# 🌍 IP地理位置语言检测功能

## 📋 功能概述

cashier-frontend现在支持基于IP地理位置的智能语言检测，能够根据用户的地理位置自动选择最合适的语言，提供更好的用户体验。

## ✅ 已实现功能

### 1. **多重检测策略**
- 🌐 **IP地理位置检测** - 根据用户IP确定国家和地区
- 💰 **货币检测** - 根据订单货币自动选择语言
- 🕐 **时区检测** - 根据浏览器时区推测地区
- 🌏 **浏览器语言检测** - 读取浏览器语言设置
- 👤 **用户手动设置** - 最高优先级，不会被覆盖

### 2. **IP服务冗余**
```javascript
// 多个IP检测服务，提高成功率
const services = [
  'https://ipapi.co/json/',      // 主要服务
  'https://ip-api.com/json/',    // 备用服务1
  'https://ipinfo.io/json'       // 备用服务2
]
```

### 3. **智能优先级**
```
1. 用户手动设置 (最高优先级)
2. 货币检测 (订单相关)
3. IP地理位置 (地理相关)
4. 时区检测 (系统相关)
5. 浏览器语言 (兜底方案)
```

## 🗺️ 支持的国家和语言

### **亚洲地区**
- 🇨🇳 中国 → 简体中文 (zh-CN)
- 🇹🇼 台湾 → 繁体中文 (zh-TW)
- 🇭🇰 香港 → 繁体中文 (zh-TW)
- 🇯🇵 日本 → 日语 (ja-JP)
- 🇰🇷 韩国 → 韩语 (ko-KR)
- 🇹🇭 泰国 → 泰语 (th-TH)
- 🇻🇳 越南 → 越南语 (vi-VN)
- 🇮🇩 印尼 → 印尼语 (id-ID)
- 🇲🇾 马来西亚 → 马来语 (ms-MY)
- 🇸🇬 新加坡 → 英语 (en-US)
- 🇵🇭 菲律宾 → 英语 (en-US)
- 🇮🇳 印度 → 英语 (en-US)

### **美洲地区**
- 🇺🇸 美国 → 英语 (en-US)
- 🇨🇦 加拿大 → 英语 (en-US)
- 🇧🇷 巴西 → 葡萄牙语 (pt-BR)
- 🇲🇽 墨西哥 → 西班牙语 (es-ES)
- 🇦🇷 阿根廷 → 西班牙语 (es-ES)

### **欧洲地区**
- 🇬🇧 英国 → 英语 (en-US)
- 🇩🇪 德国 → 德语 (de-DE)
- 🇫🇷 法国 → 法语 (fr-FR)
- 🇮🇹 意大利 → 意大利语 (it-IT)
- 🇪🇸 西班牙 → 西班牙语 (es-ES)
- 🇵🇹 葡萄牙 → 葡萄牙语 (pt-BR)
- 🇷🇺 俄罗斯 → 俄语 (ru-RU)

## 🚀 使用方法

### **自动检测 (默认)**
应用启动时自动进行语言检测，无需任何配置。

### **手动测试**
访问 `/locale-test` 页面可以：
- 🧪 测试IP地理位置检测
- 🔄 手动切换语言
- 💰 测试货币检测
- 📊 查看检测结果

### **API调用**
```javascript
import { useLocaleStore } from '@/stores/locale'

const localeStore = useLocaleStore()

// 智能检测 (推荐)
await localeStore.smartDetectLocale(paymentData)

// 单独的检测方法
await localeStore.detectAndSetLocale()           // IP检测
localeStore.detectLocaleByCurrency('USD')        // 货币检测
localeStore.detectLocaleByTimezone()             // 时区检测
```

## 🔧 配置选项

### **环境变量配置**
```bash
# .env.development 或 .env.production
VITE_ENABLE_IP_DETECTION=true
VITE_DEFAULT_LOCALE=zh-CN
VITE_IP_DETECTION_TIMEOUT=3000
```

### **代码配置**
```javascript
// src/config/locale.js
export const DETECTION_CONFIG = {
  ENABLE_IP_DETECTION: true,
  IP_TIMEOUT: 3000,
  DEFAULT_LOCALE: 'zh-CN'
}
```

## 📊 检测流程

```mermaid
graph TD
    A[应用启动] --> B{用户是否手动设置过语言?}
    B -->|是| C[使用用户设置]
    B -->|否| D[开始智能检测]
    
    D --> E{是否有订单货币?}
    E -->|是| F[货币检测]
    E -->|否| G[IP地理位置检测]
    
    F --> H{货币检测成功?}
    H -->|是| I[设置对应语言]
    H -->|否| G
    
    G --> J{IP检测成功?}
    J -->|是| K[设置对应语言]
    J -->|否| L[时区检测]
    
    L --> M{时区检测成功?}
    M -->|是| N[设置对应语言]
    M -->|否| O[浏览器语言检测]
    
    O --> P[设置最终语言]
    
    C --> Q[完成]
    I --> Q
    K --> Q
    N --> Q
    P --> Q
```

## 🛠️ 故障排除

### **问题1: IP检测失败**
**原因**: 网络问题或IP服务不可用
**解决**: 系统会自动尝试多个IP服务，最终回退到其他检测方式

### **问题2: 检测到错误的语言**
**解决**: 
1. 用户可以手动切换语言
2. 手动设置会被记住，不会被自动检测覆盖

### **问题3: 检测速度慢**
**解决**: 
1. 设置了3秒超时限制
2. 检测失败不会影响应用启动
3. 可以禁用IP检测使用其他方式

### **问题4: 某些国家检测不准确**
**解决**: 
1. 可以在 `src/config/locale.js` 中添加或修改国家映射
2. 提交issue反馈，我们会持续优化

## 📈 性能优化

### **1. 异步检测**
- IP检测不会阻塞应用启动
- 检测失败不影响用户体验

### **2. 缓存机制**
- 用户手动设置会被缓存
- 避免重复检测

### **3. 超时控制**
- 每个IP服务3秒超时
- 总检测时间不超过10秒

### **4. 服务降级**
- IP服务不可用时自动降级
- 多重检测策略确保总能确定语言

## 🔍 调试方法

### **1. 浏览器控制台**
```javascript
// 查看当前语言状态
console.log(localeStore.currentLocale)
console.log(localeStore.detectedCountry)
console.log(localeStore.isAutoDetected)
```

### **2. 测试页面**
访问 `/locale-test` 查看详细的检测信息

### **3. 网络面板**
检查IP检测请求是否成功

## 🎯 最佳实践

### **1. 用户体验**
- 首次访问自动检测语言
- 提供语言切换选项
- 记住用户选择

### **2. 性能考虑**
- 异步检测不阻塞界面
- 合理的超时设置
- 优雅的降级策略

### **3. 国际化支持**
- 支持主流语言
- 考虑地区差异
- 持续优化映射关系

现在您的支付收银台具备了完整的IP地理位置语言检测功能！🌍✨
