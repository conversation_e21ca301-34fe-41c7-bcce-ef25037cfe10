# 🎨 智能支付模板系统

这是一个**完全自动化**的支付模板系统！只需在此目录添加Vue文件，系统会自动根据文件名匹配对应的支付模板。

## ✨ 核心特性

- **🚀 零配置**：直接添加Vue文件即可，无需手动注册
- **🎯 智能匹配**：自动根据currency或channel_code匹配模板
- **🔄 动态加载**：按需加载模板，提升性能
- **📱 响应式**：所有模板都支持移动端适配

## 📁 文件结构

```
src/components/templates/
├── README.md                    # 说明文档
├── DefaultPaymentTemplate.vue   # 默认模板
├── brl.vue                     # 巴西雷亚尔模板 (自动匹配 BRL)
├── nwcs.vue                    # NWCS渠道模板 (自动匹配 nwcs)
├── PIXPaymentTemplate.vue      # PIX支付模板
└── USDTPaymentTemplate.vue     # USDT支付模板
```

## 🎯 智能匹配规则

系统会按以下优先级自动匹配模板：

1. **精确匹配 channel_code** (最高优先级)
   - `nwcs.vue` ← 匹配 `channel_code: "nwcs"`
   - `alipay.vue` ← 匹配 `channel_code: "alipay"`

2. **精确匹配 currency**
   - `brl.vue` ← 匹配 `currency: "BRL"`
   - `usd.vue` ← 匹配 `currency: "USD"`

3. **模糊匹配 channel_code**
   - `usdt.vue` ← 匹配 `channel_code: "usdt_trc20"`
   - `pix.vue` ← 匹配 `channel_code: "pix_instant"`

4. **模糊匹配 currency**
   - `usdt.vue` ← 匹配 `currency: "USDT"`

5. **默认模板**
   - `DefaultPaymentTemplate.vue` (兜底方案)

## 🚀 超简单使用方法

### 添加新模板只需3步：

1. **创建Vue文件**：在 `templates/` 目录创建 `xxx.vue`
2. **实现模板内容**：复制现有模板修改即可
3. **完成**：系统自动识别并使用！

### 实际例子：

```bash
# 想要支持微信支付？
touch src/components/templates/wechat.vue

# 想要支持欧元？
touch src/components/templates/eur.vue

# 想要支持自定义渠道？
touch src/components/templates/mychannel.vue
```

## 📋 模板文件命名规范

- **小写字母**：`brl.vue`, `usd.vue`, `nwcs.vue`
- **下划线分隔**：`usdt_trc20.vue`, `bank_transfer.vue`
- **简洁明了**：文件名应该与currency或channel_code对应

## 🎨 创建新模板

### 1. 创建模板文件

在 `src/components/templates/` 目录下创建新的Vue组件：

```vue
<template>
  <div class="custom-payment-container">
    <!-- 你的自定义模板内容 -->
  </div>
</template>

<script setup>
// Props（必须包含这些）
const props = defineProps({
  paymentData: {
    type: Object,
    required: true
  },
  timeLeft: {
    type: Number,
    default: 0
  },
  isExpired: {
    type: Boolean,
    default: false
  },
  formattedAmount: {
    type: String,
    default: ''
  }
})

// Emits（必须包含这些）
const emit = defineEmits(['copy-payment-code'])
</script>
```

### 2. 完成！

就这么简单！系统会自动：
- 🔍 发现你的新模板文件
- 🎯 根据文件名智能匹配
- 🚀 动态加载并显示

**不需要**：
- ❌ 手动注册组件
- ❌ 修改配置文件
- ❌ 重启开发服务器

## 模板Props说明

每个模板组件都会接收以下props：

- **paymentData** (Object): 完整的支付数据
  - `order_info`: 订单信息
  - `payment_info`: 支付信息
- **timeLeft** (Number): 剩余时间（秒）
- **isExpired** (Boolean): 是否已过期
- **formattedAmount** (String): 格式化后的金额

## 模板Events说明

每个模板组件需要支持以下事件：

- **copy-payment-code**: 复制支付代码时触发

## 现有模板特色

### DefaultPaymentTemplate
- 通用的支付页面设计
- 灰色背景，白色卡片
- 适用于大多数支付场景

### PIXPaymentTemplate
- 专为巴西PIX支付设计
- 绿色渐变背景
- 葡萄牙语界面
- PIX专用的UI元素

### USDTPaymentTemplate
- 专为USDT加密货币支付设计
- 青色渐变背景
- 显示网络信息（TRC20/ERC20）
- 加密货币专用说明

## 最佳实践

1. **保持一致的Props接口**：确保所有模板都接收相同的props
2. **响应式设计**：确保模板在移动端和桌面端都能正常显示
3. **国际化支持**：使用i18n进行文本国际化
4. **二维码生成**：每个模板都应该包含二维码生成逻辑
5. **错误处理**：包含适当的错误状态和过期状态处理
6. **样式隔离**：使用scoped样式避免样式冲突

## 调试技巧

1. **查看当前模板**：在浏览器控制台查看 `currentTemplate` 的值
2. **测试不同数据**：修改 `paymentData` 中的 `currency` 或 `channel_code` 来测试不同模板
3. **模板映射调试**：使用 `getPaymentTemplate()` 函数来测试映射逻辑

## 🧪 测试你的模板

### 方法1：修改URL参数
```
# 测试BRL模板
?currency=BRL

# 测试NWCS模板
?channel_code=nwcs

# 测试自定义模板
?channel_code=mychannel
```

### 方法2：浏览器控制台
```javascript
// 查看当前匹配信息
console.log('当前模板:', currentTemplate.value)

// 查看所有可用模板
import { getAvailableTemplates } from '@/config/templateMapping.js'
getAvailableTemplates().then(console.log)

// 测试匹配逻辑
import { getTemplateMatchInfo } from '@/config/templateMapping.js'
getTemplateMatchInfo({
  order_info: { currency: 'BRL' },
  payment_info: { channel_code: 'nwcs' }
}).then(console.log)
```

## 🎯 实际匹配示例

| 支付数据 | 匹配的模板 | 匹配策略 |
|---------|-----------|---------|
| `currency: "BRL"` | `brl.vue` | 精确匹配currency |
| `channel_code: "nwcs"` | `nwcs.vue` | 精确匹配channel_code |
| `channel_code: "usdt_trc20"` | `usdt.vue` | 模糊匹配channel_code |
| `currency: "EUR"` | `DefaultPaymentTemplate.vue` | 默认模板 |

## 🔧 调试技巧

1. **查看匹配日志**：打开浏览器控制台，会显示模板匹配过程
2. **强制刷新**：修改模板后按 `Ctrl+F5` 强制刷新
3. **清除缓存**：在控制台执行 `clearTemplateCache()` 清除模板缓存
