{"name": "cashier-frontend", "version": "1.0.0", "description": "支付收银台", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "build:analyze": "vite build --mode production && npx vite-bundle-analyzer dist", "preview": "vite preview", "clean": "rm -rf dist"}, "dependencies": {"@element-plus/icons-vue": "^2.3.2", "axios": "^1.5.0", "console-ban": "^5.0.0", "devtools-detector": "^2.0.23", "disable-devtool": "^0.3.9", "element-plus": "^2.10.7", "pinia": "^2.1.6", "qrcode": "^1.5.4", "vue": "^3.3.4", "vue-i18n": "^9.4.1", "vue-router": "^4.2.4"}, "devDependencies": {"@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^4.3.4", "vite": "^4.4.9", "vite-plugin-compression": "^0.5.1"}}