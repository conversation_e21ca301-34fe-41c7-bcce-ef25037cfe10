# ⏰ expire_time 倒计时功能

## 📋 功能概述

cashier-frontend现在完全基于服务器返回的 `expire_time` 字段来计算和显示倒计时，确保时间的准确性和一致性。

## ✅ 已实现功能

### 1. **基于expire_time的倒计时**
- 🕐 使用服务器返回的 `expire_time` 时间戳（秒）
- 🔄 实时计算剩余时间：`expire_time - current_time`
- ⚡ 每秒更新倒计时显示
- 🔄 每30秒重新同步服务器时间

### 2. **智能时间处理**
```javascript
// API响应格式
{
  "data": {
    "order_info": {
      "expire_time": 1755710183,  // Unix时间戳（秒）
      "order_no": "ORDER_123456",
      "currency": "USD",
      "amount": "100.00"
    }
  }
}

// 倒计时计算
const remainingTime = expire_time - Math.floor(Date.now() / 1000)
```

### 3. **倒计时状态管理**
- 🟢 **正常状态** (>10分钟): 充足时间
- 🟡 **注意状态** (5-10分钟): 注意时间  
- 🟠 **警告状态** (1-5分钟): 请尽快支付
- 🔴 **紧急状态** (≤1分钟): 即将过期
- ⚫ **过期状态** (≤0): 已过期

### 4. **时间同步机制**
- ⏱️ **主倒计时**: 每秒递减
- 🔄 **同步检查**: 每30秒重新计算
- 📡 **服务器时间**: 防止客户端时间偏差
- 🛡️ **容错处理**: 网络异常时继续倒计时

## 🔧 技术实现

### **1. Store层实现**
```javascript
// src/stores/payment.js
const loadPaymentInfo = async (token) => {
  const result = await paymentApi.getPaymentInfo(token)
  
  if (result.data.order_info?.expire_time) {
    const expireTime = parseInt(result.data.order_info.expire_time)
    const currentTime = Math.floor(Date.now() / 1000)
    const remainingTime = expireTime - currentTime
    
    timeLeft.value = Math.max(0, remainingTime)
    
    if (remainingTime <= 0) {
      paymentStatus.value = { status: -2, message: '订单已过期' }
    }
  }
}

const recalculateTimeLeft = () => {
  if (paymentData.value?.order_info?.expire_time) {
    const expireTime = parseInt(paymentData.value.order_info.expire_time)
    const currentTime = Math.floor(Date.now() / 1000)
    const remainingTime = expireTime - currentTime
    
    timeLeft.value = Math.max(0, remainingTime)
    return remainingTime > 0
  }
  return false
}
```

### **2. 页面层实现**
```javascript
// src/views/TraditionalPaymentPage.vue
const startCountdown = () => {
  // 每秒更新倒计时
  countdownTimer = setInterval(() => {
    updateTimeLeft()
    if (isExpired.value) {
      stopCountdown()
    }
  }, 1000)
  
  // 每30秒重新同步时间
  syncTimer = setInterval(() => {
    paymentStore.recalculateTimeLeft()
  }, 30000)
}
```

### **3. 工具函数**
```javascript
// src/utils/time.js
export const formatCountdown = (seconds) => {
  if (seconds <= 0) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
}

export const getCountdownStatus = (seconds) => {
  if (seconds <= 0) return { status: 'expired', message: '已过期', color: '#f56c6c' }
  if (seconds <= 60) return { status: 'critical', message: '即将过期', color: '#f56c6c' }
  if (seconds <= 300) return { status: 'warning', message: '请尽快支付', color: '#e6a23c' }
  if (seconds <= 600) return { status: 'caution', message: '注意时间', color: '#f39c12' }
  return { status: 'normal', message: '充足时间', color: '#67c23a' }
}
```

## 📊 倒计时流程

```mermaid
graph TD
    A[API返回expire_time] --> B[计算剩余时间]
    B --> C{剩余时间 > 0?}
    C -->|是| D[启动倒计时]
    C -->|否| E[设置过期状态]
    
    D --> F[每秒递减]
    F --> G{时间到0?}
    G -->|否| H[更新显示]
    G -->|是| E
    
    H --> I[30秒同步检查]
    I --> J[重新计算expire_time]
    J --> K{时间差异 > 2秒?}
    K -->|是| L[同步时间]
    K -->|否| F
    
    L --> F
    E --> M[停止倒计时]
```

## 🎨 UI显示效果

### **倒计时显示**
- ⏰ **格式**: `MM:SS` (如: `15:30`)
- 🎨 **颜色**: 根据剩余时间动态变化
- 📱 **响应式**: 适配各种屏幕尺寸

### **状态指示**
```css
/* 正常状态 - 绿色 */
.countdown.normal { color: #67c23a; }

/* 注意状态 - 橙色 */
.countdown.caution { color: #f39c12; }

/* 警告状态 - 黄色 */
.countdown.warning { color: #e6a23c; }

/* 紧急状态 - 红色 */
.countdown.critical { color: #f56c6c; }

/* 过期状态 - 灰色 */
.countdown.expired { color: #909399; }
```

## 🧪 测试功能

### **测试页面**
访问 `/countdown-test` 可以：
- 🕐 查看当前倒计时状态
- ⚙️ 设置不同的过期时间
- 🔄 测试时间同步功能
- 📊 查看时间计算详情
- 🎨 预览不同状态的样式

### **测试场景**
1. **正常倒计时**: 设置15分钟后过期
2. **即将过期**: 设置1分钟后过期
3. **已过期**: 设置负数时间
4. **时间同步**: 模拟服务器时间同步
5. **网络异常**: 测试离线状态下的倒计时

## 🔍 故障排除

### **问题1: 倒计时不准确**
**原因**: 客户端时间与服务器时间不同步
**解决**: 
- 每30秒自动重新计算
- 基于服务器的expire_time而非客户端计算

### **问题2: 倒计时突然跳跃**
**原因**: 时间同步导致的时间差异
**解决**: 
- 只有时间差异超过2秒才进行同步
- 平滑的时间过渡

### **问题3: 过期状态不正确**
**原因**: expire_time字段缺失或格式错误
**解决**: 
- 检查API响应格式
- 添加默认15分钟倒计时作为兜底

### **问题4: 页面刷新后倒计时重置**
**原因**: 没有持久化expire_time
**解决**: 
- 重新调用API获取最新的expire_time
- 基于服务器时间重新计算

## 📈 性能优化

### **1. 内存管理**
- 页面卸载时清理所有定时器
- 避免内存泄漏

### **2. 网络优化**
- 同步检查不发起网络请求
- 基于本地计算减少服务器压力

### **3. 渲染优化**
- 使用计算属性缓存格式化结果
- 避免不必要的DOM更新

## 🎯 最佳实践

### **1. 服务器端**
```json
{
  "order_info": {
    "expire_time": 1755710183,  // 必须：Unix时间戳（秒）
    "create_time": 1755709283,  // 可选：创建时间
    "timeout": 900              // 可选：超时时长（秒）
  }
}
```

### **2. 前端处理**
- ✅ 始终使用 `expire_time` 计算倒计时
- ✅ 定期同步时间防止偏差
- ✅ 优雅处理过期状态
- ✅ 提供清晰的时间提示

### **3. 用户体验**
- 🎨 直观的颜色变化
- ⚠️ 及时的过期提醒
- 📱 响应式的时间显示
- 🔄 平滑的状态转换

现在您的支付收银台具备了完整的基于 `expire_time` 的倒计时功能！⏰✨
