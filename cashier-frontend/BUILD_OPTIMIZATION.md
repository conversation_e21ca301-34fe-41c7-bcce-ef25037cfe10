# 🚀 构建优化配置

## 📦 打包配置

### 1. **Gzip压缩**
- ✅ 启用gzip压缩
- ✅ 压缩阈值：10KB以上文件
- ✅ 删除原始文件，只保留.gz文件
- ✅ 压缩率通常可达70-80%

### 2. **代码优化**
- ✅ 移除所有console.log
- ✅ 移除debugger语句
- ✅ 使用esbuild进行快速压缩
- ✅ 关闭源码映射文件

### 3. **分包策略**
```javascript
manualChunks: {
  'vendor-vue': ['vue', 'vue-router', 'pinia'],      // Vue生态
  'vendor-ui': ['element-plus', '@element-plus/icons-vue'], // UI组件
  'vendor-utils': ['vue-i18n', 'qrcode']             // 工具库
}
```

### 4. **文件命名规则**
- **JS文件**: `js/[name]-[hash].js`
- **CSS文件**: `css/[name]-[hash].css`
- **图片文件**: `img/[name]-[hash].[ext]`
- **字体文件**: `fonts/[name]-[hash].[ext]`

## 🛠️ 构建命令

### **开发环境**
```bash
npm run dev
```

### **生产构建**
```bash
npm run build:prod
```

### **构建分析**
```bash
npm run build:analyze
```

### **清理构建**
```bash
npm run clean
```

## 📊 构建结果

### **预期文件结构**
```
dist/
├── index.html
├── js/
│   ├── index-[hash].js.gz
│   ├── vendor-vue-[hash].js.gz
│   ├── vendor-ui-[hash].js.gz
│   └── vendor-utils-[hash].js.gz
├── css/
│   └── index-[hash].css.gz
├── img/
│   └── [images].gz
└── fonts/
    └── [fonts].gz
```

### **优化效果**
- 📦 **文件大小**: 减少70-80%
- ⚡ **加载速度**: 提升3-5倍
- 🗂️ **文件数量**: 合理分包，减少HTTP请求
- 🔒 **代码安全**: 移除调试信息

## ⚙️ 服务器配置

### **Nginx配置示例**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # Gzip配置
    gzip on;
    gzip_static on;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }

    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### **Apache配置示例**
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    RewriteRule ^index\.html$ - [L]
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule . /index.html [L]
</IfModule>

<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>
```

## 🔍 性能监控

### **构建分析工具**
1. **Bundle Analyzer**: 分析包大小
2. **Lighthouse**: 性能评分
3. **WebPageTest**: 加载速度测试

### **关键指标**
- **FCP**: First Contentful Paint < 1.5s
- **LCP**: Largest Contentful Paint < 2.5s
- **FID**: First Input Delay < 100ms
- **CLS**: Cumulative Layout Shift < 0.1

## 🚀 部署建议

1. **使用CDN**: 加速静态资源加载
2. **启用HTTP/2**: 提升并发请求性能
3. **配置缓存策略**: 合理设置缓存时间
4. **监控性能**: 定期检查加载速度
5. **渐进式加载**: 按需加载非关键资源

## 📝 注意事项

1. **gzip压缩**: 服务器需要支持gzip解压
2. **浏览器兼容**: 确保目标浏览器支持
3. **缓存策略**: 更新时需要清理缓存
4. **源码保护**: 生产环境不包含源码映射
5. **错误监控**: 生产环境需要错误收集系统
