# 传统支付页面 Vue 版本

这是一个基于 Vue 3 的传统支付页面实现，用于显示第三方支付接口返回的支付信息。

## 项目特点

- ✅ **纯净版本**：只保留必要的依赖，移除了所有不需要的组件和库
- ✅ **传统支付流程**：显示第三方返回的支付信息，用户在当前页面完成支付
- ✅ **自动状态检查**：后台定期检查支付状态，前端实时更新
- ✅ **响应式设计**：完美适配手机和桌面设备

## 项目结构

```
cashier-frontend/
├── src/
│   ├── views/
│   │   └── TraditionalPaymentPage.vue  # 传统支付页面
│   ├── router/
│   │   └── index.js                    # 路由配置
│   ├── App.vue                         # 根组件
│   ├── main.js                         # 入口文件
│   └── style.css                       # 全局样式
├── package.json                        # 项目配置
├── vite.config.js                      # Vite 配置
└── index.html                          # HTML 模板
```

## 依赖说明

### 生产依赖
- `vue`: Vue 3 框架
- `vue-router`: Vue 路由

### 开发依赖
- `@vitejs/plugin-vue`: Vue 插件
- `vite`: 构建工具

## 功能说明

### 支付页面功能
1. **Token 解析**：解析 URL 中的支付 Token
2. **支付信息显示**：展示订单信息、支付金额、二维码等
3. **倒计时**：显示支付剩余时间
4. **状态监控**：每 5 秒自动检查支付状态
5. **响应式布局**：适配各种设备尺寸

### API 接口
- `GET /api/cashier/info/{token}` - 获取支付信息
- `POST /internal/payment/check-status` - 内部状态检查（不暴露给前端用户）

## 使用方法

### 开发环境
```bash
npm install
npm run dev
```

### 生产构建
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 访问方式

支付页面 URL 格式：
```
http://localhost:3000/payment/{token}
```

其中 `{token}` 是后端生成的支付 Token。

## 数据流程

1. **后端生成 Token**：包含订单信息和第三方支付数据
2. **前端解析 Token**：获取基本订单信息
3. **获取支付信息**：调用 API 获取完整的支付数据
4. **显示支付界面**：展示二维码、订单信息等
5. **状态监控**：定期检查支付状态并更新界面
6. **支付完成**：显示支付成功状态

## 特色功能

### 传统支付体验
- 不需要点击"立即支付"按钮
- 直接显示第三方返回的支付信息
- 用户在当前页面完成支付操作

### 自动状态更新
- 后台每 5 秒检查一次支付状态
- 支付成功后自动更新页面显示
- 无需用户手动刷新页面

### 响应式设计
- 桌面端：卡片式布局，信息清晰
- 移动端：垂直布局，适配小屏幕
- 二维码：自适应大小，方便扫描

## 技术特点

- **轻量级**：只使用必要的依赖，包体积小
- **现代化**：基于 Vue 3 Composition API
- **高性能**：Vite 构建，开发体验好
- **易维护**：代码结构清晰，注释完整
