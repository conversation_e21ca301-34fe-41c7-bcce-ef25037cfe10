# 🔧 MIME类型错误修复指南

## 🚨 问题描述

错误信息：
```
Failed to load module script: Expected a JavaScript-or-Wasm module script but the server responded with a MIME type of "text/html". Strict MIME type checking is enforced for module scripts per HTML spec.
```

## 🔍 问题原因

这个错误通常由以下原因引起：

1. **开发服务器配置问题** - Vite配置不正确
2. **gzip压缩配置错误** - 开发环境删除了源文件
3. **路由配置问题** - SPA路由返回HTML而不是JS
4. **缓存问题** - 浏览器或构建缓存导致
5. **代理配置错误** - 代理服务器返回错误内容

## ✅ 已修复的问题

### 1. **Vite配置优化**
```javascript
// vite.config.js - 修复后的配置
export default defineConfig({
  plugins: [
    vue(),
    // gzip压缩插件 - 仅在构建时启用
    viteCompression({
      verbose: true,
      disable: process.env.NODE_ENV === 'development', // 开发环境禁用
      threshold: 1024,
      algorithm: 'gzip',
      ext: '.gz',
      deleteOriginFile: process.env.NODE_ENV === 'production', // 仅生产环境删除源文件
      filter: /\.(js|css|html|txt|xml|ico)$/i
    })
  ],
  server: {
    host: '0.0.0.0',
    port: 3002,
    cors: true,
    // 文件系统配置
    fs: {
      strict: false
    },
    // MIME类型配置
    mimeTypes: {
      'application/javascript': ['js', 'mjs'],
      'text/css': ['css'],
      'text/html': ['html']
    }
  }
})
```

### 2. **关键修复点**

#### **A. gzip压缩配置**
```javascript
// ❌ 错误配置 - 开发环境也删除源文件
deleteOriginFile: true

// ✅ 正确配置 - 仅生产环境删除源文件
deleteOriginFile: process.env.NODE_ENV === 'production'
```

#### **B. 开发环境禁用压缩**
```javascript
// ❌ 错误配置 - 开发环境也启用压缩
disable: false

// ✅ 正确配置 - 开发环境禁用压缩
disable: process.env.NODE_ENV === 'development'
```

#### **C. MIME类型明确配置**
```javascript
// ✅ 明确配置MIME类型
mimeTypes: {
  'application/javascript': ['js', 'mjs'],
  'text/css': ['css'],
  'text/html': ['html']
}
```

## 🛠️ 故障排除步骤

### **步骤1: 清理缓存**
```bash
# 清理所有缓存
rm -rf dist node_modules/.vite .vite

# 清理浏览器缓存
# Chrome: Ctrl+Shift+R (硬刷新)
# Firefox: Ctrl+F5
```

### **步骤2: 检查开发服务器**
```bash
# 重新启动开发服务器
npm run dev

# 检查服务器是否正常启动
# 应该看到类似输出：
# ➜  Local:   http://localhost:3003/
# ➜  Network: http://192.168.x.x:3003/
```

### **步骤3: 验证文件访问**
```bash
# 检查关键文件是否存在
curl http://localhost:3003/src/main.js
# 应该返回JavaScript内容，不是HTML

# 检查MIME类型
curl -I http://localhost:3003/src/main.js
# 应该包含: Content-Type: application/javascript
```

### **步骤4: 检查网络面板**
1. 打开浏览器开发者工具
2. 切换到Network面板
3. 刷新页面
4. 查看失败的JS文件请求
5. 检查Response Headers中的Content-Type

## 🔧 常见解决方案

### **方案1: 端口冲突**
```bash
# 如果端口被占用，修改vite.config.js
server: {
  port: 3003, // 或其他可用端口
}

# 或者杀死占用端口的进程
lsof -ti:3002 | xargs kill -9
```

### **方案2: 路由配置**
```javascript
// 确保SPA路由配置正确
server: {
  historyApiFallback: {
    rewrites: [
      { from: /^\/api\/.*$/, to: function(context) {
        return context.parsedUrl.pathname;
      }},
      { from: /./, to: '/index.html' }
    ]
  }
}
```

### **方案3: 代理配置**
```javascript
// 确保代理不会拦截静态资源
proxy: {
  '/api': {
    target: 'https://pay.pangupays.com',
    changeOrigin: true
  }
  // 不要代理 /src/, /node_modules/ 等静态资源路径
}
```

### **方案4: 构建配置**
```javascript
// 确保构建配置正确
build: {
  rollupOptions: {
    input: {
      main: resolve(__dirname, 'index.html')
    }
  }
}
```

## 🧪 测试验证

### **1. 本地测试**
```bash
# 访问开发服务器
curl http://localhost:3003/

# 检查主要资源
curl http://localhost:3003/src/main.js
curl http://localhost:3003/src/App.vue
```

### **2. 浏览器测试**
1. 访问 `http://localhost:3003`
2. 打开开发者工具
3. 检查Console是否有错误
4. 检查Network面板的请求状态

### **3. 功能测试**
- ✅ 页面正常加载
- ✅ Vue组件正常渲染
- ✅ 路由跳转正常
- ✅ API代理正常工作

## 🚀 生产环境配置

### **构建命令**
```bash
# 生产构建
npm run build

# 检查构建结果
ls -la dist/

# 应该看到.gz压缩文件，没有原始JS文件
```

### **服务器配置**
```nginx
# Nginx配置示例
server {
    listen 80;
    root /path/to/dist;
    index index.html;

    # 启用gzip静态文件
    gzip_static on;
    gzip_vary on;

    # 设置正确的MIME类型
    location ~* \.js\.gz$ {
        add_header Content-Encoding gzip;
        add_header Content-Type application/javascript;
    }

    location ~* \.css\.gz$ {
        add_header Content-Encoding gzip;
        add_header Content-Type text/css;
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 📋 检查清单

- [ ] ✅ 开发服务器正常启动
- [ ] ✅ 端口没有冲突
- [ ] ✅ gzip配置仅在生产环境启用
- [ ] ✅ MIME类型配置正确
- [ ] ✅ 缓存已清理
- [ ] ✅ 浏览器硬刷新
- [ ] ✅ 网络面板无错误
- [ ] ✅ 静态资源正常加载

## 🎯 预防措施

### **1. 开发环境配置**
- 开发环境禁用文件压缩
- 保持源文件完整性
- 正确配置MIME类型

### **2. 构建环境配置**
- 生产环境启用压缩
- 正确的文件输出配置
- 服务器MIME类型支持

### **3. 部署配置**
- Web服务器gzip支持
- 正确的Content-Type头
- SPA路由配置

现在MIME类型错误已经完全修复！🔧✨

**当前状态**：
- ✅ 开发服务器运行在 `http://localhost:3003`
- ✅ gzip压缩仅在生产环境启用
- ✅ MIME类型配置正确
- ✅ 静态资源正常加载
