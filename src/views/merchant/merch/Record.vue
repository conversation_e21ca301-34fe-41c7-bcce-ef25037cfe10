<script setup lang="ts">
import { ref, reactive, watch, h } from "vue";
import type { MerchantRecord, MerchantRecordQuery } from "@/api/merchant";
import { billTypeMap } from "@/api/bill";
import type { PaginationProps } from "@pureadmin/table";
import { getPickerShortcuts } from "@/utils/date";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { PureTableBar } from "@/components/RePureTableBar";
import Search from "@iconify-icons/ep/search";
import Refresh from "@iconify-icons/ep/refresh";
import { getMerchantRecord } from "@/api/merchant";
import { message } from "@/utils/message";
import { ElTag } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  merchant: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "close"): void;
}>();

const dialogVisible = ref(props.visible);
const loading = ref(false);
const dataList = ref<MerchantRecord[]>([]);
const formRef = ref();
const tableRef = ref();

// 搜索表单
const form = reactive({
  daterange: null as [string, string] | null
});

// 分页配置
const pagination = ref<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

const size = ref("default");

// 表格列配置
const columns = [
  {
    label: "序号",
    type: "index",
    width: 80
  },
  {
    label: "变动类型",
    prop: "type",
    width: 120,
    cellRenderer: ({ row, props }) =>
      h(
        ElTag,
        {
          size: props.size,
          type: "primary",
          effect: "light"
        },
        () => billTypeMap[row.type] || "-"
      )
  },
  {
    label: "变动前金额",
    prop: "before_amount",
    width: 120,
    formatter: ({ before_amount }) => formatAmount(before_amount)
  },
  {
    label: "变动金额",
    prop: "amount",
    width: 120,
    formatter: ({ amount }) => formatAmount(amount)
  },
  {
    label: "变动后金额",
    prop: "after_amount",
    width: 120,
    formatter: ({ after_amount }) => formatAmount(after_amount)
  },
  {
    label: "备注",
    prop: "remark"
  },
  {
    label: "操作时间",
    prop: "create_time",
    width: 180
  }
];

// 格式化金额
const formatAmount = (amount: string | number): string => {
  return amount ? Number(amount).toFixed(2) : "0.00";
};

// 加载数据
const loadData = async () => {
  if (loading.value) return;
  loading.value = true;

  try {
    const params: MerchantRecordQuery = {
      merchant_id: props.merchant?.id,
      page: pagination.value.currentPage,
      pageSize: pagination.value.pageSize,
      start_time: form.daterange?.[0],
      end_time: form.daterange?.[1]
    };

    if (!params.merchant_id) {
      message("商户ID不能为空", { type: "warning" });
      return;
    }

    const { data } = await getMerchantRecord(params);
    dataList.value = data.list;
    pagination.value.total = data.total;
  } catch (error: any) {
    message(error.message || "获取数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 分页方法
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val;
  pagination.value.currentPage = 1;
  loadData();
};

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val;
  loadData();
};

// 重置表单
const resetForm = () => {
  if (!formRef.value) return;
  formRef.value.resetFields();
  pagination.value.currentPage = 1;
  loadData();
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  newVal => {
    dialogVisible.value = newVal;
  }
);

watch(
  () => dialogVisible.value,
  async newVal => {
    emit("update:visible", newVal);
    if (newVal) {
      await loadData();
    } else {
      emit("close");
    }
  }
);

defineExpose({
  loadData
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`资金明细 - ${merchant?.username || ''}`"
    width="950px"
    top="5vh"
    destroy-on-close
    class="record-dialog"
  >
    <div class="main">
      <!-- 搜索表单 -->
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
      >
        <el-form-item label="时间范围" prop="daterange">
          <el-date-picker
            v-model="form.daterange"
            type="datetimerange"
            :shortcuts="getPickerShortcuts()"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="!w-[380px]"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon(Search)"
            :loading="loading"
            @click="loadData"
          >
            搜索
          </el-button>
          <el-button :icon="useRenderIcon(Refresh)" @click="resetForm">
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 表格部分 -->
      <div class="table-container">
        <PureTableBar title="资金记录" :columns="columns" @refresh="loadData">
          <template v-slot="{ size, dynamicColumns }">
            <pure-table
              ref="tableRef"
              adaptive
              align-whole="center"
              row-key="id"
              showOverflowTooltip
              table-layout="auto"
              :loading="loading"
              :size="size"
              :data="dataList"
              :columns="dynamicColumns"
              :pagination="pagination"
              :header-cell-style="{
                background: 'var(--el-fill-color-light)',
                color: 'var(--el-text-color-primary)'
              }"
              @page-size-change="handleSizeChange"
              @page-current-change="handleCurrentChange"
            />
          </template>
        </PureTableBar>
      </div>
    </div>
  </el-dialog>
</template>

<style lang="scss" scoped>
.record-dialog {
  :deep(.el-dialog__body) {
    padding: 10px 20px;
    height: 80vh;
    overflow: hidden;
  }
}

.main {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-form {
  flex-shrink: 0;
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.pure-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .el-table {
    flex: 1;
    overflow: hidden;

    .el-table__inner-wrapper {
      height: 100%;
    }

    .el-table__header-wrapper {
      width: 100% !important;
    }

    .el-table__body-wrapper {
      height: calc(100% - 40px) !important;
      overflow-y: auto;
    }
  }
}
</style>
