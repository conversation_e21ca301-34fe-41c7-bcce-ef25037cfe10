<script setup>
import { ref, watch, onMounted } from "vue";
import { message } from "@/utils/message";
import {
  getAllCurrencies,
  getMerchantCurrencies,
  setMerchantCurrency
} from "@/api/currency";

// 表单数据
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  merchantId: {
    type: [Number, String],
    required: false,
    default: ""
  },
  merchantName: {
    type: String,
    default: ""
  }
});

const emit = defineEmits(["update:modelValue", "close"]);

// 对话框可见性
const visible = ref(props.modelValue);

// 货币列表
const currencyList = ref([]);

// 已选货币ID列表
const selectedCurrencyIds = ref([]);

// 加载状态
const loading = ref(false);
const submitting = ref(false);

// 加载货币列表和商户已选货币
const loadData = async () => {
  if (!props.merchantId) {
    message("请先选择商户", { type: "warning" });
    loading.value = false;
    return;
  }

  loading.value = true;
  try {
    // 获取所有启用的货币
    const { data: allCurrencies } = await getAllCurrencies({
      status: 1
    });

    currencyList.value = Array.isArray(allCurrencies) ? allCurrencies : [];

    // 获取商户已选货币
    const response = await getMerchantCurrencies({
      merchant_id: props.merchantId
    });

    // 增加对返回数据格式的检查
    let merchantCurrencyIds = [];

    if (response && response.data && Array.isArray(response.data)) {
      // 如果数据是数组格式
      merchantCurrencyIds = response.data.map(
        item => item.currency_id || item.id
      );
    } else if (
      response &&
      response.data &&
      response.data.list &&
      Array.isArray(response.data.list)
    ) {
      // 如果数据在list字段中
      merchantCurrencyIds = response.data.list.map(
        item => item.currency_id || item.id
      );
    } else {
      console.warn("商户币种数据格式不正确:", response);
    }

    selectedCurrencyIds.value = merchantCurrencyIds;
  } catch (error) {
    console.error("Failed to load data:", error);
    message("加载数据失败", { type: "error" });
    selectedCurrencyIds.value = []; // 设置为空数组避免后续错误
  } finally {
    loading.value = false;
  }
};

// 提交表单
const submitForm = async () => {
  if (selectedCurrencyIds.value.length === 0) {
    message("请至少选择一种货币", { type: "warning" });
    return;
  }

  submitting.value = true;
  try {
    await setMerchantCurrency({
      merchant_id: props.merchantId,
      currencies: selectedCurrencyIds.value
    });

    message("保存成功", { type: "success" });
    visible.value = false;
  } catch (error) {
    console.error("Failed to save merchant currencies:", error);
    message("保存失败", { type: "error" });
  } finally {
    submitting.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
};

// 监听对话框可见性变化
watch(
  () => props.modelValue,
  val => {
    visible.value = val;
    if (val && props.merchantId) {
      loadData();
    }
  }
);

// 监听内部对话框可见性变化
watch(
  () => visible.value,
  val => {
    emit("update:modelValue", val);
    if (!val) {
      emit("close");
    }
  }
);

// 组件挂载时加载数据
onMounted(() => {
  if (visible.value && props.merchantId) {
    loadData();
  }
});
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="`货币配置 - ${merchantName}`"
    width="600px"
  >
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else class="currency-config">
      <el-alert type="info" show-icon :closable="false">
        <p>请选择该商户支持的货币类型。商户只能使用已选择的货币进行交易。</p>
        <p>可以根据商户的业务需求灵活配置，包括基准货币在内的所有货币都可以自由选择。</p>
      </el-alert>

      <div class="currency-selection">
        <el-transfer
          v-model="selectedCurrencyIds"
          :titles="['可选货币', '已选货币']"
          :data="
            currencyList.map(c => ({
              key: c.id,
              label: `${c.name} (${c.code}) ${c.symbol}`,
              disabled: false,
              country: c.country || '-',
              is_base: c.is_base === 1
            }))
          "
          :props="{
            key: 'key',
            label: 'label'
          }"
          filterable
        >
          <template #default="{ option }">
            <div class="currency-item">
              <span class="currency-name">{{ option.label }}</span>
              <span class="currency-country">{{ option.country }}</span>
              <el-tag v-if="option.is_base" size="small" type="info"
                >基准</el-tag
              >
            </div>
          </template>
        </el-transfer>
      </div>

      <div class="tips">
        <p>提示: 请根据商户业务需要选择支持的货币类型。</p>
      </div>
    </div>

    <template #footer>
      <el-button :disabled="submitting" @click="handleCancel">取消</el-button>
      <el-button type="primary" :loading="submitting" @click="submitForm">
        保存
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.loading-container {
  padding: 20px;
}

.currency-config {
  padding: 10px 0;
}

.currency-selection {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.tips {
  font-size: 12px;
  color: #909399;
  text-align: center;
  margin-top: 10px;
}

.currency-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.currency-name {
  flex: 1;
}

.currency-country {
  color: #909399;
  font-size: 12px;
}
</style>
