<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { message } from "@/utils/message";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { PureTableBar } from "@/components/RePureTableBar";
import Delete from "@iconify-icons/ep/delete";
import EditPen from "@iconify-icons/ep/edit-pen";
import AddFill from "@iconify-icons/ri/add-circle-line";
import type { FormRules } from "element-plus";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "群组列表"
  },
  row: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(["update:visible", "success"]);

// 表格数据
const dataList = ref([]);
const loading = ref(false);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

// 表格列配置
const columns = [
  {
    label: "群组ID",
    prop: "group_id",
    minWidth: 120
  },
  {
    label: "群组名称",
    prop: "group_name",
    minWidth: 150
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
];

// 新增弹窗数据
const addDialogVisible = ref(false);
const addDialogTitle = ref("新增群组");
const submitLoading = ref(false);
const addFormRef = ref();

// 新增表单
const addForm = reactive({
  group_id: "",
  group_name: ""
});

// 表单验证规则
const rules = reactive<FormRules>({
  group_id: [{ required: true, message: "请输入群组ID", trigger: "blur" }],
  group_name: [{ required: true, message: "请输入群组名称", trigger: "blur" }]
});

// 加载数据
async function loadData() {
  loading.value = true;
  try {
    // TODO: 调用获取群组列表接口
    dataList.value = [
      {
        group_id: "123456789",
        group_name: "测试群组1"
      },
      {
        group_id: "987654321",
        group_name: "测试群组2"
      }
    ];
    pagination.total = dataList.value.length;
  } catch (error) {
    message(error.message || "加载失败", { type: "error" });
  } finally {
    loading.value = false;
  }
}

// 绑定群组
async function handleBind(row) {
  try {
    // TODO: 调用绑定群组接口
    message("绑定成功", { type: "success" });
    emit("success");
    closeDialog();
  } catch (error) {
    message(error.message || "绑定失败", { type: "error" });
  }
}

// 关闭弹窗
function closeDialog() {
  emit("update:visible", false);
}

// 打开新增弹窗
function handleAdd() {
  addDialogVisible.value = true;
  addForm.group_id = "";
  addForm.group_name = "";
}

// 关闭新增弹窗
function closeAddDialog() {
  addDialogVisible.value = false;
  addFormRef.value?.resetFields();
}

// 提交表单
async function onSubmit() {
  if (!addFormRef.value) return;
  await addFormRef.value.validate(async (valid, fields) => {
    if (valid) {
      submitLoading.value = true;
      try {
        // TODO: 调用添加群组接口
        message("添加成功", { type: "success" });
        closeAddDialog();
        loadData();
      } catch (error) {
        message(error.message || "添加失败", { type: "error" });
      } finally {
        submitLoading.value = false;
      }
    }
  });
}

watch(
  () => props.visible,
  val => {
    if (val) {
      loadData();
    }
  }
);
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="800px"
    destroy-on-close
    class="group-dialog"
    @update:model-value="emit('update:visible', $event)"
    @close="closeDialog"
  >
    <div class="dialog-content">
      <PureTableBar title="群组列表" :columns="columns" @refresh="loadData">
        <template #buttons>
          <el-button
            type="primary"
            :icon="useRenderIcon(AddFill)"
            @click="handleAdd"
          >
            新增群组
          </el-button>
        </template>
        <template v-slot="{ size, dynamicColumns }">
          <pure-table
            adaptive
            align-whole="center"
            row-key="id"
            showOverflowTooltip
            table-layout="auto"
            :loading="loading"
            :size="size"
            :data="dataList"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
          >
            <template #operation="{ row }">
              <el-button
                class="reset-margin"
                link
                type="primary"
                :size="size"
                :icon="useRenderIcon(EditPen)"
                @click="handleBind(row)"
              >
                绑定
              </el-button>
            </template>
          </pure-table>
        </template>
      </PureTableBar>
    </div>
  </el-dialog>

  <!-- 新增群组弹窗 -->
  <el-dialog
    v-model="addDialogVisible"
    :title="addDialogTitle"
    width="500px"
    destroy-on-close
    @close="closeAddDialog"
  >
    <el-form
      ref="addFormRef"
      :model="addForm"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="群组ID" prop="group_id">
        <el-input v-model="addForm.group_id" placeholder="请输入群组ID" />
      </el-form-item>
      <el-form-item label="群组名称" prop="group_name">
        <el-input v-model="addForm.group_name" placeholder="请输入群组名称" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="closeAddDialog">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="onSubmit">
        确 定
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.group-dialog {
  :deep(.el-dialog__body) {
    padding: 10px 20px;
    height: 60vh;
    overflow: hidden;
  }
}

.dialog-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.pure-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .el-table {
    flex: 1;
    overflow: hidden;

    .el-table__inner-wrapper {
      height: 100%;
    }

    .el-table__header-wrapper {
      width: 100% !important;
    }

    .el-table__body-wrapper {
      height: calc(100% - 40px) !important;
      overflow-y: auto;
    }
  }
}

.reset-margin {
  margin-left: 0 !important;
  padding: 0 4px !important;
}
</style>
