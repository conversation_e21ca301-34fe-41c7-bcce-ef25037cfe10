<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { message } from "@/utils/message";
import { merchantSettlementInfo, merchantSettlement } from "@/api/merchant";

interface Merchant {
  merchant_id?: string | number;
  username?: string;
  balance?: number;
  prepaid_amount?: number;
  id?: number;
}

interface UnsettledPeriod {
  name: string;
  count: number;
  amount: number;
  start_time: string;
  end_time: string;
}

const props = defineProps<{
  visible: boolean;
  merchant: Merchant | null;
}>();

const emit = defineEmits(["update:visible", "confirm"]);

// 结算信息
const todayAmount = ref<number>(0);
const todaySettlement = ref<number>(0);
const loading = ref<boolean>(false);
const remark = ref<string>("");
const settlementAmount = ref<number>(0);
const adjustedSettlementAmount = ref<number>(0);
const useAdjustedAmount = ref<boolean>(false);
const totalUnsettledAmount = ref<number>(0);
const totalUnsettledCount = ref<number>(0);
const unsettledPeriods = ref<Record<string, UnsettledPeriod>>({});

// 结算周期
const settlePeriod = ref<string>("today");
const settlePeriodOptions = [
  { label: "今日", value: "today" },
  { label: "昨日", value: "yesterday" },
  { label: "本周", value: "this_week" },
  { label: "上周", value: "last_week" },
  { label: "本月", value: "this_month" },
  { label: "全部未结算", value: "all" }
];

// 是否更新订单结算状态
const updateOrderStatus = ref<boolean>(true);

// 当前选择的周期的未结算信息
const currentPeriodUnsettled = computed(() => {
  if (unsettledPeriods.value && settlePeriod.value in unsettledPeriods.value) {
    return unsettledPeriods.value[settlePeriod.value];
  }
  return {
    name: "未知",
    count: 0,
    amount: 0,
    start_time: "",
    end_time: ""
  };
});

// 结算后的预期结果
const settlementResult = computed(() => {
  let finalAmount = 0;

  if (useAdjustedAmount.value) {
    finalAmount = adjustedSettlementAmount.value;
  } else if (
    currentPeriodUnsettled.value &&
    currentPeriodUnsettled.value.count > 0
  ) {
    finalAmount = currentPeriodUnsettled.value.amount;
  } else {
    finalAmount = settlementAmount.value;
  }

  if (finalAmount <= 0) {
    return "无需结算";
  }

  // 新的计算方式：预付款减少结算金额，余额归零
  const newBalance = 0; // 余额归零
  const newPrepaid = Number(props.merchant?.prepaid_amount || 0) - finalAmount;

  return `结算后: 余额=${newBalance.toFixed(2)}, 预付款=${newPrepaid.toFixed(2)}`;
});

// 结算金额文本
const settlementText = computed(() => {
  if (!currentPeriodUnsettled.value) return "--";

  const period = currentPeriodUnsettled.value.name;
  const count = currentPeriodUnsettled.value.count;
  const amount = Number(currentPeriodUnsettled.value.amount || 0).toFixed(2);

  return `${period}未结算：${count}笔，共${amount}元`;
});

// 计算结算金额
const settlement = computed(() => {
  const prepaid = Number(props.merchant?.prepaid_amount || 0).toFixed(2);
  const balance = Number(props.merchant?.balance || 0).toFixed(2);
  const today = Number(todayAmount.value || 0).toFixed(2);

  if (useAdjustedAmount.value) {
    // 考虑今日跑量的调整后结算金额
    const result = adjustedSettlementAmount.value.toFixed(2);
    return `${prepaid} - (${balance} + ${today}) = ${result}`;
  } else {
    // 基础结算金额
    const result = settlementAmount.value.toFixed(2);
    return `${prepaid} - ${balance} = ${result}`;
  }
});

// 结算公式显示
const settlementFormula = computed(() => {
  const prepaid = Number(props.merchant?.prepaid_amount || 0).toFixed(2);
  const balance = Number(props.merchant?.balance || 0).toFixed(2);
  const today = Number(todayAmount.value || 0).toFixed(2);

  if (useAdjustedAmount.value) {
    // 考虑今日跑量的调整后结算金额
    const result = adjustedSettlementAmount.value.toFixed(2);
    return `${prepaid} - (${balance} + ${today}) = ${result}`;
  } else {
    // 基础结算金额
    const result = settlementAmount.value.toFixed(2);
    return `${prepaid} - ${balance} = ${result}`;
  }
});

// 获取结算信息
const fetchSettlementInfo = async () => {
  if (!props.merchant || !props.merchant.id) return;

  loading.value = true;
  try {
    const res = await merchantSettlementInfo(props.merchant.id);
    if (res && (res.code === 0 || res.code === 200)) {
      todayAmount.value = Number(res.data.today_amount || 0);
      todaySettlement.value = Number(res.data.today_settlement || 0);
      settlementAmount.value = Number(res.data.settlement_amount || 0);
      adjustedSettlementAmount.value = Number(
        res.data.adjusted_settlement_amount || 0
      );
      totalUnsettledAmount.value = Number(res.data.total_unsettled_amount || 0);
      totalUnsettledCount.value = Number(res.data.total_unsettled_count || 0);

      // 更新未结算周期数据
      if (res.data.unsettled_periods) {
        unsettledPeriods.value = res.data.unsettled_periods;
      }
    } else {
      message(`获取结算信息失败：${res.msg}`, {
        type: "error"
      });
    }
  } catch (error) {
    message(`获取结算信息失败：${error.message || "未知错误"}`, {
      type: "error"
    });
  } finally {
    loading.value = false;
  }
};

// 监听商户变化和对话框状态变化（使用防抖避免重复请求）
let fetchTimer: number | null = null;
watch(
  [() => props.merchant, () => props.visible],
  ([newMerchant, newVisible]) => {
    // 清除之前的定时器
    if (fetchTimer) {
      clearTimeout(fetchTimer);
      fetchTimer = null;
    }

    // 如果商户存在且对话框可见，则获取数据
    if (newMerchant && newVisible) {
      // 使用setTimeout进行简单防抖，避免重复请求
      fetchTimer = setTimeout(() => {
        fetchSettlementInfo();
      }, 50);
    }
  },
  { immediate: true }
);

const handleClose = () => {
  remark.value = "";
  useAdjustedAmount.value = false;
  settlePeriod.value = "today";
  updateOrderStatus.value = true;
  emit("update:visible", false);
};

const handleConfirm = async () => {
  if (!props.merchant || !props.merchant.id) {
    message("商户信息不完整", { type: "error" });
    return;
  }

  // 根据选择的结算方式确定结算金额
  let finalAmount = 0;

  if (useAdjustedAmount.value) {
    // 使用调整后的结算金额（考虑今日跑量）
    finalAmount = adjustedSettlementAmount.value;
  } else if (
    currentPeriodUnsettled.value &&
    currentPeriodUnsettled.value.count > 0
  ) {
    // 使用当前选择周期的未结算金额
    finalAmount = currentPeriodUnsettled.value.amount;
  } else {
    // 使用基础结算金额
    finalAmount = settlementAmount.value;
  }

  if (finalAmount <= 0) {
    message("结算金额必须大于0", { type: "error" });
    return;
  }

  try {
    loading.value = true;
    const res = await merchantSettlement({
      merchant_id: props.merchant.id,
      settlement_amount: finalAmount,
      remark: remark.value || "管理员手动结算",
      settle_period: settlePeriod.value,
      update_order_status: updateOrderStatus.value ? 1 : 0
    });

    if (res && (res.code === 0 || res.code === 200)) {
      message("结算成功", { type: "success" });
      emit("confirm", res.data);
      handleClose();
    } else {
      message(`结算失败：${res.msg}`, { type: "error" });
    }
  } catch (error) {
    message(`结算失败：${error.message || "未知错误"}`, { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 切换结算周期
const handlePeriodChange = () => {
  // 如果周期有未结算订单，可以自动调整结算金额
  if (
    currentPeriodUnsettled.value &&
    currentPeriodUnsettled.value.count > 0 &&
    currentPeriodUnsettled.value.amount > 0
  ) {
    useAdjustedAmount.value = false; // 关闭调整模式，使用当前周期的未结算金额
  }
};
</script>

<template>
  <el-dialog
    :model-value="visible"
    title="商户结算"
    width="450px"
    :close-on-click-modal="false"
    @update:model-value="emit('update:visible', $event)"
    @close="handleClose"
  >
    <el-form v-loading="loading">
      <div class="flex flex-col gap-2 mb-4">
        <!-- 商户基本信息 -->
        <div class="flex gap-3">
          <span class="text-gray-500 w-24 text-right">商户号</span>
          <span class="font-bold">{{ merchant?.merchant_id }}</span>
        </div>

        <div class="flex gap-3">
          <span class="text-gray-500 w-24 text-right">商户账号</span>
          <span class="font-bold">{{ merchant?.username }}</span>
        </div>

        <div class="flex gap-3">
          <span class="text-gray-500 w-24 text-right">余额</span>
          <span class="text-red-500 font-bold">{{
            Number(merchant?.balance || 0).toFixed(2)
          }}</span>
        </div>

        <div class="flex gap-3">
          <span class="text-gray-500 w-24 text-right">预付款</span>
          <span class="text-red-500 font-bold">{{
            Number(merchant?.prepaid_amount || 0).toFixed(2)
          }}</span>
        </div>

        <div class="custom-divider">
          <el-divider />
        </div>

        <!-- 交易数据 -->
        <div class="flex gap-3">
          <span class="text-gray-500 w-24 text-right">今日跑量</span>
          <span class="text-blue-500 font-bold">{{
            todayAmount.toFixed(2)
          }}</span>
        </div>

        <div class="flex gap-3">
          <span class="text-gray-500 w-24 text-right">今日已结算</span>
          <span class="text-blue-500 font-bold">{{
            todaySettlement.toFixed(2)
          }}</span>
        </div>

        <div class="flex gap-3">
          <span class="text-gray-500 w-24 text-right">未结算总额</span>
          <span class="text-blue-500 font-bold">{{
            totalUnsettledAmount.toFixed(2)
          }}</span>
          <span class="text-gray-400 text-sm">
            (共 {{ totalUnsettledCount }} 笔)
          </span>
        </div>

        <div class="custom-divider">
          <el-divider />
        </div>

        <!-- 结算设置 -->
        <div class="flex gap-3 items-center">
          <span class="text-gray-500 w-24 text-right">结算周期</span>
          <el-select
            v-model="settlePeriod"
            placeholder="选择结算周期"
            @change="handlePeriodChange"
          >
            <el-option
              v-for="item in settlePeriodOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>

        <div class="flex gap-3 items-center">
          <span class="text-gray-500 w-24 text-right">未结算明细</span>
          <span class="text-orange-500 font-bold">{{ settlementText }}</span>
        </div>

        <div class="flex gap-3 items-center mt-2">
          <span class="text-gray-500 w-24 text-right">结算公式</span>
          <span class="text-red-500 font-bold">{{ settlementFormula }}</span>
        </div>

        <div class="flex gap-3 items-center mt-2">
          <span class="text-gray-500 w-24 text-right">结算结果</span>
          <span class="text-green-500 font-bold">{{ settlementResult }}</span>
        </div>

        <div class="flex gap-3 items-center mt-2">
          <span class="text-gray-500 w-24 text-right">考虑跑量</span>
          <el-switch v-model="useAdjustedAmount" />
          <span class="text-gray-400 text-sm">{{
            useAdjustedAmount ? "已计入今日跑量" : "仅计算预付款-余额"
          }}</span>
        </div>

        <div class="flex gap-3 items-center mt-2">
          <span class="text-gray-500 w-24 text-right">更新订单</span>
          <el-switch v-model="updateOrderStatus" />
          <span class="text-gray-400 text-sm">{{
            updateOrderStatus ? "更新订单结算状态" : "不更新订单状态"
          }}</span>
        </div>

        <div class="flex gap-3 items-center mt-2">
          <span class="text-gray-500 w-24 text-right">结算备注</span>
          <el-input v-model="remark" placeholder="请输入结算备注" />
        </div>
      </div>
    </el-form>

    <template #footer>
      <div class="flex justify-end gap-2">
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          确定结算
        </el-button>
        <el-button @click="handleClose">取消结算</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
.custom-divider :deep(.el-divider--horizontal) {
  margin: 8px 0;
}
</style>
