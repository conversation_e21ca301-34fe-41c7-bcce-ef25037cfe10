<template>
  <el-dialog
    :model-value="visible"
    title="USDT配置"
    width="650px"
    @close="handleClose"
  >
    <el-form :model="form" label-width="100px" class="usdt-form">
      <!-- U地址1 -->
      <el-form-item label="U地址1">
        <div class="input-container">
          <el-input
            v-model="form.u_address1"
            type="textarea"
            :rows="2"
            class="mr-10"
          />
          <div class="button-container">
            <el-button @click="mockModify">修改</el-button>
            <el-button @click="mockUpload">上传U地址图片</el-button>
            <el-button @click="mockDetect">测 试</el-button>
          </div>
        </div>
      </el-form-item>

      <!-- U地址2 -->
      <el-form-item label="U地址2">
        <div class="input-container">
          <el-input v-model="form.u_address2" class="mr-10" />
          <div class="button-container">
            <el-button @click="mockModify">修改</el-button>
            <el-button @click="mockUpload">上传U地址图片</el-button>
          </div>
        </div>
      </el-form-item>

      <!-- U地址确认开关 -->
      <el-form-item label="U地址确认开关">
        <div class="switch-container">
          <el-switch v-model="form.confirm_switch" />
          <span class="switch-desc">开启后，群发地址机器人会发确认信息。</span>
        </div>
      </el-form-item>

      <!-- 启用U自动下发 -->
      <el-form-item label="启用U自动下发">
        <div class="switch-container">
          <el-switch v-model="form.auto_send_switch" />
          <span class="switch-desc">开启后，可以自动下发USDT。</span>
        </div>
      </el-form-item>

      <!-- U自动下发被授权地址 - 条件显示 -->
      <el-form-item v-if="form.auto_send_switch" label="U自动下发被授权地址">
        <div class="input-container">
          <el-input v-model="form.authorized_address" class="mr-10" />
          <div class="button-container">
            <el-button @click="mockCopy">拷贝</el-button>
            <el-button @click="mockQuery">查询记录</el-button>
          </div>
        </div>
        <div class="desc-text">转账所需的能量和带宽由被授权地址承担。</div>
      </el-form-item>

      <!-- U自动下发授权地址 - 条件显示 -->
      <el-form-item v-if="form.auto_send_switch" label="U自动下发授权地址">
        <div class="input-container">
          <el-input
            v-model="form.grant_address"
            placeholder="请输入授权地址"
            class="mb-10"
          />
        </div>

        <div class="grant-container">
          <div class="grant-label">授权额度：</div>
          <div class="grant-controls">
            <el-button class="control-btn" @click="decreaseAmount">-</el-button>
            <el-input-number
              v-model="form.grant_amount"
              :min="0"
              :controls="false"
              class="grant-input"
            />
            <el-button class="control-btn" @click="increaseAmount">+</el-button>
          </div>
          <div class="status-tag">
            <el-tag :type="form.grant_status ? 'success' : 'info'">
              {{ form.grant_status ? "已授权" : "未授权" }}
            </el-tag>
          </div>
          <el-button @click="mockGrant">授权</el-button>
        </div>
      </el-form-item>
    </el-form>

    <!-- 温馨提示 -->
    <div class="tips-container">
      <div class="tips-header">
        <el-icon><WarningFilled /></el-icon> 温馨提示：
      </div>
      <ol class="tips-list">
        <li>没有授权时U从被授权地址转出，因此U需要预先存入被授权地址。</li>
        <li>有授权时从U授权地址转出，因此需要预先存入授权地址。</li>
        <li>
          若有授权转账，消耗的带宽和能量由被授权地址承担，有授权时需配置能量和带宽。
        </li>
        <li>
          普通转账：带宽[345]能量[65000]，授权转账：带宽[378]能量[90000]（授权转账的能量套餐不符合条件，请使用系统的能量）。
        </li>
        <li>
          被授权地址能量不足系统将自动补充负重，普通转账或授权转账消耗后自动补充。
        </li>
        <li>被授权地址免费带宽不足时系统将自动补充并重试。</li>
        <li>"自动下发"在充值产币自动分配时优先开启。</li>
        <li>
          "授权额度"是单笔每次转账的最大值，即每次下发给用户的U不超过授权额度。
        </li>
      </ol>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from "vue";
import { message } from "@/utils/message";
import { WarningFilled } from "@element-plus/icons-vue";
import {
  getConfig,
  saveUsdt,
  testUsdtAddress,
  uploadUsdtImage
} from "@/api/bot";

const props = defineProps({
  visible: Boolean,
  bot: Object
});
const emit = defineEmits(["update:visible", "submit"]);

const form = ref({
  u_address1: "测试地址\n<code>TYEMUMKBmkTRMa1CNh7xKNYZ2mTVFw9f1b</code>",
  u_address2: "222222333",
  confirm_switch: true,
  auto_send_switch: true,
  authorized_address: "TJPfBZZMgSA4e8KzC9vAjPw8yKwPJQ1Tse",
  grant_address: "",
  grant_amount: 100000,
  grant_status: false
});

function decreaseAmount() {
  if (form.value.grant_amount > 0) {
    form.value.grant_amount -= 1000;
  }
}

function increaseAmount() {
  form.value.grant_amount += 1000;
}

watch(
  () => props.visible,
  async val => {
    if (val && props.bot) {
      try {
        const res = await getConfig(props.bot.id);
        if (res.data?.usdt) {
          form.value = {
            ...form.value,
            ...res.data.usdt
          };
        }
      } catch (error) {
        message("获取配置失败：" + error.message, { type: "error" });
      }
    }
  }
);

function handleClose() {
  emit("update:visible", false);
}

async function handleSubmit() {
  try {
    await saveUsdt(props.bot.id, form.value);
    emit("update:visible", false);
    message("USDT配置已保存", { type: "success" });
  } catch (error) {
    message("保存失败：" + error.message, { type: "error" });
  }
}

// mock交互方法
function mockModify() {
  message("模拟修改成功", { type: "success" });
}

async function mockUpload() {
  try {
    // 创建文件选择器
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";

    input.onchange = async () => {
      if (input.files && input.files[0]) {
        const formData = new FormData();
        formData.append("image", input.files[0]);

        const res = await uploadUsdtImage(props.bot.id, formData);
        message("USDT地址图片上传成功", { type: "success" });
      }
    };

    input.click();
  } catch (error) {
    message("上传失败：" + error.message, { type: "error" });
  }
}

async function mockDetect() {
  try {
    await testUsdtAddress(props.bot.id, form.value.u_address1);
    message("USDT地址测试通过", { type: "success" });
  } catch (error) {
    message("测试失败：" + error.message, { type: "error" });
  }
}

function mockCopy() {
  message("已拷贝", { type: "success" });
}
function mockQuery() {
  message("模拟查询记录", { type: "info" });
}
function mockGrant() {
  form.value.grant_status = true;
  message("模拟授权成功", { type: "success" });
}
</script>

<style scoped>
.usdt-form {
  width: 100%;
}

.input-container {
  display: flex;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 10px;
}

.mr-10 {
  margin-right: 10px;
  flex: 1;
}

.mb-10 {
  margin-bottom: 10px;
  width: 100%;
}

.button-container {
  display: flex;
  white-space: nowrap;
}

.button-container .el-button {
  margin-left: 10px;
}

.button-container .el-button:first-child {
  margin-left: 0;
}

.switch-container {
  display: flex;
  align-items: center;
  height: 32px;
}

.switch-desc {
  color: #606266;
  font-size: 14px;
  margin-left: 10px;
}

.desc-text {
  color: #606266;
  font-size: 14px;
  margin-top: 5px;
}

.grant-container {
  display: flex;
  align-items: center;
}

.grant-label {
  color: #606266;
  white-space: nowrap;
  margin-right: 5px;
}

.grant-controls {
  display: flex;
  align-items: center;
}

.control-btn {
  padding: 8px 12px;
}

.grant-input {
  width: 150px;
  margin: 0 5px;
}

.status-tag {
  margin: 0 15px;
}

/* 提示容器样式 */
.tips-container {
  background-color: #fdf6ec;
  border-radius: 4px;
  padding: 16px;
  margin-top: 25px;
}

.tips-header {
  display: flex;
  align-items: center;
  color: #e6a23c;
  font-weight: bold;
  margin-bottom: 10px;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
  color: #606266;
  font-size: 14px;
}

.tips-list li {
  margin-bottom: 8px;
  line-height: 1.6;
}

.tips-list li:last-child {
  margin-bottom: 0;
}
</style>
