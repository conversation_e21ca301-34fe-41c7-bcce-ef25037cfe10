<script setup>
import { ref, watch, onMounted } from "vue";
import { message } from "@/utils/message";
import { getCurrencyList } from "@/api/currency";

// 表单数据
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["update:modelValue", "submit"]);

// 对话框可见性
const visible = ref(props.modelValue);

// 货币列表
const currencyList = ref([]);

// 基准货币
const baseCurrency = ref(null);

// 加载状态
const loading = ref(false);

// 加载货币列表
const loadCurrencies = async () => {
  loading.value = true;
  try {
    // 获取所有货币
    const { data } = await getCurrencyList({
      pageSize: 100, // 大页面尺寸确保获取所有货币
      status: 1 // 只获取启用状态的货币
    });

    currencyList.value = data.list.map(currency => ({
      ...currency,
      old_rate: currency.rate,
      new_rate: currency.rate
    }));

    // 找出基准货币
    baseCurrency.value = currencyList.value.find(c => c.is_base === 1);

    // 去掉基准货币(因为基准货币汇率固定为1)
    currencyList.value = currencyList.value.filter(c => c.is_base !== 1);
  } catch (error) {
    console.error("Failed to load currencies:", error);
    message("加载货币数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 提交表单
const submitForm = () => {
  // 过滤出有变更的汇率记录
  const updatedRates = currencyList.value
    .filter(c => c.old_rate !== c.new_rate)
    .map(c => ({
      id: c.id,
      old_rate: c.old_rate,
      rate: c.new_rate
    }));

  if (updatedRates.length === 0) {
    message("没有汇率变更", { type: "warning" });
    return;
  }

  // 发送给父组件处理
  emit("submit", updatedRates);
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
};

// 监听对话框可见性变化
watch(
  () => props.modelValue,
  val => {
    visible.value = val;
    if (val) {
      loadCurrencies();
    }
  }
);

// 监听内部对话框可见性变化
watch(
  () => visible.value,
  val => {
    emit("update:modelValue", val);
  }
);

// 组件挂载时加载数据
onMounted(() => {
  if (visible.value) {
    loadCurrencies();
  }
});
</script>

<template>
  <el-dialog v-model="visible" title="汇率批量设置" width="650px">
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>

    <div v-else>
      <div class="info-box">
        <div class="info-title">基准货币信息</div>
        <div v-if="baseCurrency" class="base-currency-info">
          <span>{{ baseCurrency.name }} ({{ baseCurrency.code }})</span>
          <span>汇率: 1.000000</span>
        </div>
        <div v-else class="base-currency-warning">
          未设置基准货币,请先设置基准货币
        </div>
      </div>

      <div class="rate-list">
        <el-table
          :data="currencyList"
          border
          style="width: 100%"
          max-height="400"
        >
          <el-table-column label="货币名称" prop="name" min-width="120">
            <template #default="{ row }">
              <div>{{ row.name }} ({{ row.code }})</div>
              <div class="currency-symbol">{{ row.symbol }}</div>
            </template>
          </el-table-column>

          <el-table-column
            label="当前汇率"
            prop="old_rate"
            width="120"
            align="center"
          >
            <template #default="{ row }">
              {{ Number(row.old_rate).toFixed(6) }}
            </template>
          </el-table-column>

          <el-table-column
            label="新汇率"
            prop="new_rate"
            width="200"
            align="center"
          >
            <template #default="{ row }">
              <el-input-number
                v-model="row.new_rate"
                :min="0"
                :precision="6"
                :step="0.000001"
                controls-position="right"
                style="width: 180px"
              />
            </template>
          </el-table-column>

          <el-table-column label="变化率" width="120" align="center">
            <template #default="{ row }">
              <span
                :class="[
                  'change-rate',
                  {
                    increase: Number(row.new_rate) > Number(row.old_rate),
                    decrease: Number(row.new_rate) < Number(row.old_rate)
                  }
                ]"
              >
                {{ calculateChangeRate(row.old_rate, row.new_rate) }}
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="submitForm">保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
// 计算变化率
function calculateChangeRate(oldRate, newRate) {
  if (!oldRate || oldRate === 0) return "0%";
  const change = ((newRate - oldRate) / oldRate) * 100;
  const sign = change > 0 ? "+" : "";
  return `${sign}${change.toFixed(2)}%`;
}
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.info-box {
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 4px;
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
}

.info-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #67c23a;
}

.base-currency-info {
  display: flex;
  justify-content: space-between;
}

.base-currency-warning {
  color: #f56c6c;
}

.currency-symbol {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.change-rate {
  font-weight: bold;
}

.increase {
  color: #f56c6c;
}

.decrease {
  color: #67c23a;
}
</style>
