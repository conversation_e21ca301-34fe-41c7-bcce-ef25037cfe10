<script setup>
import { ref, watch } from "vue";
import { message } from "@/utils/message";
import { addCurrency, updateCurrency } from "@/api/currency";

// 表单数据
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  currency: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(["update:modelValue", "submit"]);

// 表单引用
const formRef = ref(null);

// 对话框可见性
const visible = ref(props.modelValue);

// 表单数据
const form = ref({
  id: "",
  name: "",
  code: "",
  symbol: "",
  is_base: 0,
  rate: 1.0,
  precision: 2,
  country: "",
  sort: 0,
  status: 1
});

// 表单规则
const rules = {
  name: [
    { required: true, message: "请输入货币名称", trigger: "blur" },
    { max: 50, message: "货币名称不能超过50个字符", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入货币代码", trigger: "blur" },
    { max: 10, message: "货币代码不能超过10个字符", trigger: "blur" },
    { pattern: /^[A-Za-z]+$/, message: "货币代码只能包含字母", trigger: "blur" }
  ],
  symbol: [
    { required: true, message: "请输入货币符号", trigger: "blur" },
    { max: 10, message: "货币符号不能超过10个字符", trigger: "blur" }
  ],
  rate: [
    { required: true, message: "请输入汇率", trigger: "blur" },
    { type: "number", message: "汇率必须为数字", trigger: "blur" },
    { 
      validator: (rule, value, callback) => {
        if (value <= 0) {
          callback(new Error("汇率必须大于0"));
        } else {
          callback();
        }
      }, 
      trigger: "blur" 
    }
  ],
  precision: [
    { required: true, message: "请输入精度", trigger: "blur" },
    { type: "number", message: "精度必须为数字", trigger: "blur" },
    { 
      validator: (rule, value, callback) => {
        if (value === null || value === undefined) {
          callback(new Error("请输入精度"));
        } else if (!Number.isInteger(value)) {
          callback(new Error("精度必须为整数"));
        } else if (value < 0 || value > 8) {
          callback(new Error("精度范围必须在0-8之间"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  country: [{ max: 50, message: "国家名称不能超过50个字符", trigger: "blur" }],
  sort: [{ type: "number", message: "排序必须为数字", trigger: "blur" }]
};

// 加载表单数据
const loadForm = () => {
  if (props.currency) {
    form.value = { ...props.currency };
  }
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (valid) {
      try {
        // 如果有ID则是编辑,否则是新增
        if (form.value.id) {
          await updateCurrency(form.value);
          message("修改成功", { type: "success" });
        } else {
          await addCurrency(form.value);
          message("添加成功", { type: "success" });
        }

        emit("submit");
      } catch (error) {
        console.error("表单提交失败:", error);
        message(`提交失败: ${error.message || "未知错误"}`, {
          type: "error"
        });
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
};

// 取消操作
const handleCancel = () => {
  resetForm();
  visible.value = false;
};

// 监听对话框可见性变化
watch(
  () => props.modelValue,
  val => {
    visible.value = val;
    if (val) {
      loadForm();
    }
  }
);

// 监听内部对话框可见性变化
watch(
  () => visible.value,
  val => {
    emit("update:modelValue", val);
  }
);

// 监听货币数据变化
watch(
  () => props.currency,
  () => {
    if (visible.value) {
      loadForm();
    }
  },
  { deep: true }
);
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="form.id ? '编辑货币' : '新增货币'"
    width="500px"
    destroy-on-close
    @closed="resetForm"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="right"
    >
      <el-form-item label="货币名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入货币名称" />
      </el-form-item>

      <el-form-item label="货币代码" prop="code">
        <el-input
          v-model="form.code"
          placeholder="请输入货币代码"
          :disabled="!!form.id"
        />
        <div class="form-tip">仅支持字母,例如: CNY, USD</div>
      </el-form-item>

      <el-form-item label="货币符号" prop="symbol">
        <el-input v-model="form.symbol" placeholder="请输入货币符号" />
        <div class="form-tip">例如: ¥, $, €</div>
      </el-form-item>

      <el-form-item label="所属国家" prop="country">
        <el-input v-model="form.country" placeholder="请输入所属国家" />
      </el-form-item>

      <el-form-item label="基准货币" prop="is_base">
        <el-switch
          v-model="form.is_base"
          :active-value="1"
          :inactive-value="0"
        />
        <div class="form-tip">仅一种货币能设为基准货币</div>
      </el-form-item>

      <el-form-item label="汇率" prop="rate">
        <el-input-number
          v-model="form.rate"
          :precision="6"
          :step="0.000001"
          :min="0"
          controls-position="right"
          style="width: 100%"
        />
        <div class="form-tip">相对于基准货币的汇率</div>
        <div 
          class="form-tip error-tip" 
          v-if="form.rate !== null && form.rate <= 0"
        >
          汇率必须大于0
        </div>
      </el-form-item>

      <el-form-item label="精度" prop="precision">
        <el-input-number
          v-model="form.precision"
          :min="0"
          :max="8"
          :step="1"
          controls-position="right"
          style="width: 100%"
        />
        <div class="form-tip">金额小数点位数</div>
        <div 
          class="form-tip error-tip"
          v-if="form.precision !== null && (form.precision < 0 || form.precision > 8)"
        >
          精度范围必须在0-8之间
        </div>
      </el-form-item>

      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="form.sort"
          :min="0"
          :step="1"
          controls-position="right"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :label="1">启用</el-radio>
          <el-radio :label="0">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.error-tip {
  color: #f56c6c;
}
</style>
