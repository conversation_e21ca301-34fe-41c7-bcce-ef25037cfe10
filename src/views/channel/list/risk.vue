<script setup lang="ts">
import { ref, defineExpose, onMounted } from "vue";
import { getRiskConfig, saveRiskConfig } from "@/api/channel";
import { message } from "@/utils/message";

const props = defineProps({
  channelId: {
    type: [String, Number],
    required: true
  },
  source: {
    type: String,
    default: "channel"
  }
});

const emit = defineEmits(["success"]);
const formRef = ref();
const form = ref({
  channel_id: props.channelId,
  dispatch_mode: 1,
  check_account: false,
  query_account: false,
  float_amount: false,
  float_type: null,
  float_max: 0,
  allow_over_range: false,
  allow_float_repeat: false,
  auto_download: false,
  auto_download_timeout: false,
  auto_download_fixed: false,
  timeout_lock: false,
  single_order: false,
  single_amount: false,
  auto_download_unconfirmed: false,
  limit_pull_times: false,
  pay_page_timeout: 10,
  order_confirm_timeout: 10,
  min_amount: 0,
  max_amount: 0,
  daily_limit: 0,
  daily_order_limit: 0,
  check_account_info: "",
  query_account_interval: 10,
  expiry_time: 10,
  unconfirmed_count: 3,
  pull_times_count: 3,
  allow_claim_edit: false,
  allow_claim_cancel: false,
  claim_confirm: false,
  allow_agent_edit_limit: false,
  allow_agent_edit_min_limit: false,
  allow_agent_edit_max_limit: false,
  fixed_code_mode: false,
  fixed_code_range: "",
  fixed_code_mixed_mode: false,
  limit_agent_review_orders: false,
  max_review_orders: 5,
  pay_data_type: 1,
  allow_type_config: false,
  type_config_options: []
});

async function getConfig() {
  try {
    const res = await getRiskConfig(Number(props.channelId), props.source);
    if (res.data) {
      form.value = res.data;
    }
  } catch (error) {
    message("获取配置失败", { type: "error" });
  }
}

async function onSubmit() {
  try {
    // 确保所有必要的数据都存在
    const saveData = {
      ...form.value,
      channel_id: Number(props.channelId), // 确保是数字
      source: props.source
    };

    await saveRiskConfig(saveData);
    message("风控配置保存成功", { type: "success" });
    emit("success");
  } catch (error) {
    message("风控配置保存失败", { type: "error" });
    throw error;
  }
}

defineExpose({
  onSubmit
});

onMounted(() => {
  getConfig();
});
</script>

<template>
  <el-form ref="formRef" :model="form" class="mt-4 risk-form">
    <div class="risk-section">
      <h3 class="section-title">基础配置</h3>
      <el-form-item v-if="props.source === 'channel'" label="允许支付类型配置">
        <el-switch v-model="form.allow_type_config" />
        <span class="text-red-500 ml-2">[开启后支付类型可以单独配置]</span>
      </el-form-item>
      <el-form-item label="选单模式">
        <el-radio-group v-model="form.dispatch_mode">
          <el-radio :value="1">直接派单</el-radio>
          <el-radio :value="2">提交后派单</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="开启账号预检测">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.check_account" />
          <span class="text-red-500">[提前检测账号是否可用，默认代付代收]</span>
        </div>
      </el-form-item>

      <el-form-item label="开启账号轮询">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.query_account" />
          <span class="text-red-500">[开启后代收代付账号轮询]</span>
        </div>
      </el-form-item>

      <el-form-item label="支付数据类型">
        <el-select
          v-model="form.pay_data_type"
          placeholder="请选择支付数据类型"
        >
          <el-option label="支付链接" :value="1" />
          <el-option label="支付数据" :value="2" />
          <el-option label="支付地址和数据" :value="3" />
          <el-option label="APP数据" :value="4" />
          <el-option label="表单数据" :value="5" />
        </el-select>
      </el-form-item>

      <!-- <el-form-item label="币种">
        <el-select v-model="form.currency" placeholder="请选择币种">
          <el-option label="巴西雷亚尔" :value="1" />
          <el-option label="墨西哥比索" :value="2" />
          <el-option label="阿根廷比索" :value="3" />
          <el-option label="智利比索" :value="4" />
          <el-option label="哥伦比亚比索" :value="5" />
        </el-select>
      </el-form-item> -->
    </div>

    <div class="risk-section">
      <h3 class="section-title">金额配置</h3>
      <el-form-item label="开启金额浮动">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.float_amount" />
          <span class="text-red-500">[开启后金额随机浮动]</span>
        </div>
      </el-form-item>

      <el-row
        v-if="form.float_amount"
        style="
          margin-bottom: 10px;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
        "
        :gutter="10"
      >
        <el-col :span="4" style="padding-right: 5px">
          <el-form-item label="浮动类型" class="m-0" style="margin-bottom: 0">
            <el-select
              v-model="form.float_type"
              placeholder="请选择浮动类型"
              style="min-width: 80px"
            >
              <el-option label="上浮" :value="1" />
              <el-option label="下浮" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" style="padding-left: 36px">
          <el-form-item label="浮动最大值" class="m-0" style="margin-bottom: 0">
            <el-input-number
              v-model="form.float_max"
              :min="0"
              style="min-width: 80px"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6" style="padding-left: 5px">
          <el-form-item
            label="允许超范围分配"
            class="m-0"
            style="margin-bottom: 0"
          >
            <el-switch v-model="form.allow_over_range" />
          </el-form-item>
        </el-col>

        <el-col :span="6" style="padding-left: 5px">
          <el-form-item
            label="允许浮动金额重复"
            class="m-0"
            style="margin-bottom: 0"
          >
            <el-switch v-model="form.allow_float_repeat" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="单笔最小金额">
        <el-input-number
          v-model="form.min_amount"
          :min="0"
          :precision="2"
          :step="100"
        />
        <span class="text-red-500 ml-2">[0表示不限制]</span>
      </el-form-item>

      <el-form-item label="单笔最大金额">
        <el-input-number
          v-model="form.max_amount"
          :min="0"
          :precision="2"
          :step="100"
        />
        <span class="text-red-500 ml-2">[0表示不限制]</span>
      </el-form-item>

      <el-form-item label="单日限额">
        <el-input-number
          v-model="form.daily_limit"
          :min="0"
          :precision="2"
          :step="1000"
        />
        <span class="text-red-500 ml-2">[0表示不限制]</span>
      </el-form-item>

      <el-form-item label="单日限单">
        <el-input-number
          v-model="form.daily_order_limit"
          :min="0"
          :precision="0"
          :step="1"
        />
        <span class="text-red-500 ml-2">[0表示不限制]</span>
      </el-form-item>
    </div>

    <div class="risk-section">
      <h3 class="section-title">自动化配置</h3>
      <el-form-item label="开启成功自动下码">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.auto_download" />
          <span class="text-red-500">[订单处理后(成功)收款码自动下线]</span>
        </div>
      </el-form-item>

      <el-form-item label="开启接单自动下码">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.auto_download_timeout" />
          <span class="text-red-500">
            [订单处理后(成功或者超时等)收款码自动下线]
          </span>
        </div>
      </el-form-item>

      <el-form-item label="开启定时自动下码">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.auto_download_fixed" />
          <span class="text-red-500">[1个码只能接一单，默认代收代付]</span>
          <el-input-number
            v-if="form.auto_download_fixed"
            v-model="form.expiry_time"
            :min="1"
            placeholder="请输入码过期时间（分钟）"
            style="margin-top: 10px"
          />
        </div>
      </el-form-item>

      <el-form-item label="开启接单锁码">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.timeout_lock" />
          <span class="text-red-500">
            [接单时锁定，处理后(成功或者超时等)释放]
          </span>
        </div>
      </el-form-item>

      <el-form-item label="开启一个码只接一单">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.single_order" />
        </div>
      </el-form-item>

      <el-form-item label="开启一个码同金额只接一单">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.single_amount" />
        </div>
      </el-form-item>

      <el-form-item label="开启连续X个超时未确认自动下码">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.auto_download_unconfirmed" />
        </div>
        <el-form-item
          v-if="form.auto_download_unconfirmed"
          label="连续超时数量"
        >
          <el-input-number
            v-model="form.unconfirmed_count"
            :min="1"
            placeholder="请输入连续超时数量"
          />
        </el-form-item>
      </el-form-item>

      <el-form-item label="开启限制拉单次数">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.limit_pull_times" />
        </div>
        <el-form-item v-if="form.limit_pull_times" label="限制拉单次数">
          <el-input-number
            v-model="form.pull_times_count"
            :min="1"
            placeholder="请输入拉单次数"
          />
        </el-form-item>
      </el-form-item>

      <el-form-item label="支付页面未支付删除时间">
        <el-input-number v-model="form.pay_page_timeout" :min="0" /> 分钟
        <span class="text-red-500 ml-2">[0表示不删除]</span>
      </el-form-item>

      <el-form-item label="后台订单未确认删除时间">
        <el-input-number v-model="form.order_confirm_timeout" :min="0" /> 分钟
        <span class="text-red-500 ml-2">[0表示不删除]</span>
      </el-form-item>
    </div>

    <div class="risk-section">
      <h3 class="section-title">权限配置</h3>
      <el-form-item label="开启允许代理编辑单笔限额">
        <div class="nested-form-content">
          <el-switch v-model="form.allow_agent_edit_limit" />

          <div v-if="form.allow_agent_edit_limit" class="nested-form-items">
            <el-form-item label="开启仅允许代理编辑单笔最小限额">
              <el-switch v-model="form.allow_agent_edit_min_limit" />
            </el-form-item>

            <el-form-item label="开启仅允许代理编辑单笔最大限额">
              <el-switch v-model="form.allow_agent_edit_max_limit" />
            </el-form-item>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="开启允许理赔单编辑限制">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.allow_claim_edit" />
        </div>
      </el-form-item>

      <el-form-item label="开启允许理赔单取消">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.allow_claim_cancel" />
        </div>
      </el-form-item>

      <el-form-item label="开启理赔单确认">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.claim_confirm" />
        </div>
      </el-form-item>

      <el-form-item label="开启固码模式">
        <div style="display: flex; align-items: center; gap: 10px">
          <div style="display: flex; align-items: center">
            <el-switch v-model="form.fixed_code_mode" />
            <span v-if="form.fixed_code_mode" style="margin-left: 10px"
              >固码金额范围</span
            >
            <el-input
              v-if="form.fixed_code_mode"
              v-model="form.fixed_code_range"
              placeholder="范围格式100|200|300"
              style="margin-left: 10px; width: 200px"
            />
            <span v-if="form.fixed_code_mode" style="margin-left: 10px"
              >开启固码混合模式</span
            >
            <el-switch
              v-if="form.fixed_code_mode"
              v-model="form.fixed_code_mixed_mode"
              style="margin-left: 10px"
            />
          </div>
        </div>
      </el-form-item>

      <el-form-item label="开启限制代理审核中订单数">
        <div style="display: flex; align-items: center; gap: 10px">
          <el-switch v-model="form.limit_agent_review_orders" />
        </div>
        <el-form-item
          v-if="form.limit_agent_review_orders"
          label="审核中最大订单数"
        >
          <el-input-number
            v-model="form.max_review_orders"
            :min="1"
            placeholder="请输入最大订单数"
          />
          <span class="text-red-500 ml-2">[超过数量自动不派单]</span>
        </el-form-item>
      </el-form-item>
    </div>

    <el-form-item v-if="form.check_account" label="账号预检测信息">
      <el-input
        v-model="form.check_account_info"
        placeholder="请输入账号预检测信息"
      />
    </el-form-item>

    <el-form-item v-if="form.query_account" label="账号轮询间隔">
      <el-input-number v-model="form.query_account_interval" :min="1" /> 秒
    </el-form-item>

    <div v-if="props.source === 'channel'" class="risk-section">
      <h3 class="section-title">支付类型风控权限</h3>
      <el-form-item label="允许支付类型配置">
        <el-switch v-model="form.allow_type_config" />
        <span class="text-red-500 ml-2">[开启后支付类型可以单独配置]</span>
      </el-form-item>

      <el-form-item v-if="form.allow_type_config" label="允许配置的选项">
        <el-checkbox-group v-model="form.type_config_options">
          <el-checkbox label="float_amount" value="float_amount">
            金额浮动
          </el-checkbox>
          <el-checkbox label="auto_download" value="auto_download">
            自动下码
          </el-checkbox>
          <el-checkbox
            label="auto_download_timeout"
            value="auto_download_timeout"
          >
            接单自动下码
          </el-checkbox>
          <el-checkbox label="auto_download_fixed" value="auto_download_fixed">
            定时自动下码
          </el-checkbox>
          <el-checkbox label="timeout_lock" value="timeout_lock">
            接单锁码
          </el-checkbox>
          <el-checkbox label="single_order" value="single_order">
            一码一单
          </el-checkbox>
          <el-checkbox label="single_amount" value="single_amount">
            同金额一单
          </el-checkbox>
          <el-checkbox label="limit_pull_times" value="limit_pull_times">
            开启限制拉单次数
          </el-checkbox>
          <el-checkbox
            label="auto_download_unconfirmed"
            value="auto_download_unconfirmed"
          >
            连续超时自动下码
          </el-checkbox>
          <el-checkbox label="fixed_code_mode" value="fixed_code_mode">
            固码模式
          </el-checkbox>
          <el-checkbox label="allow_claim_edit" value="allow_claim_edit">
            理赔单编辑
          </el-checkbox>
          <el-checkbox label="allow_claim_cancel" value="allow_claim_cancel">
            理赔单取消
          </el-checkbox>
          <el-checkbox label="claim_confirm" value="claim_confirm">
            理赔单确认
          </el-checkbox>
          <el-checkbox
            value="limit_agent_review_orders"
            label="限制代理审核订单"
          >
            限制代理审核订单
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </div>
  </el-form>
</template>

<style lang="scss" scoped>
.risk-form {
  margin-left: 0;
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 20px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--el-border-color);
    border-radius: 3px;
  }

  .risk-section {
    background: var(--el-bg-color-page);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .section-title {
      color: var(--el-text-color-primary);
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--el-border-color-light);
    }
  }

  :deep(.el-form-item) {
    margin-right: 0;
    display: flex;

    .el-form-item__content {
      display: flex;
      align-items: center;

      .el-switch {
        margin-right: 15px;
      }

      span {
        line-height: 32px;
      }

      .el-input-number + span,
      .el-input + span {
        margin-left: 15px;
      }

      > div {
        // display: flex;
        // align-items: center;
        gap: 15px;
      }
    }

    .el-form-item__label {
      font-weight: 500;
    }
  }

  .el-input-number + span {
    line-height: 32px;
  }

  :deep(.el-input-number) {
    width: 160px;
  }

  :deep(.el-select) {
    width: 160px;
  }
}

.nested-form-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.nested-form-items {
  :deep(.el-form-item) {
    margin-bottom: 16px;
    margin-left: -120px; /* 向左对齐，根据实际label宽度调整 */

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
