<template>
  <el-dialog
    v-model="dialogVisible"
    title="费率记录"
    width="950px"
    top="5vh"
    destroy-on-close
    class="record-dialog"
  >
    <div class="main">
      <!-- 搜索表单 -->
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
      >
        <el-form-item label="时间范围" prop="daterange">
          <el-date-picker
            v-model="form.daterange"
            type="datetimerange"
            :shortcuts="getPickerShortcuts()"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            class="!w-[380px]"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon(Search)"
            :loading="loading"
            @click="getList"
          >
            搜索
          </el-button>
          <el-button :icon="useRenderIcon(Refresh)" @click="resetForm">
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <PureTableBar title="费率记录" :columns="columns" @refresh="getList">
        <template v-slot="{ size, dynamicColumns }">
          <pure-table
            ref="tableRef"
            adaptive
            align-whole="center"
            row-key="id"
            showOverflowTooltip
            table-layout="auto"
            :loading="loading"
            :size="size"
            :data="dataList"
            :columns="dynamicColumns"
            :pagination="pagination"
            :header-cell-style="{
              background: 'var(--el-fill-color-light)',
              color: 'var(--el-text-color-primary)'
            }"
            @page-size-change="handleSizeChange"
            @page-current-change="handleCurrentChange"
          />
        </template>
      </PureTableBar>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import dayjs from "dayjs";
import { message } from "@/utils/message";
import { getFeeRecordList } from "@/api/channel";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getPickerShortcuts } from "@/utils/date";
import Search from "@iconify-icons/ep/search";
import Refresh from "@iconify-icons/ep/refresh";
import type { PaginationProps } from "@pureadmin/table";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "close"): void;
}>();

const dialogVisible = ref(props.visible);
const loading = ref(false);
const dataList = ref([]);
const formRef = ref();
const tableRef = ref();
let currentChannelId = ref(null);

// 搜索表单
const form = reactive({
  daterange: null as [string, string] | null
});

const columns = [
  {
    label: "ID",
    prop: "id",
    width: 80
  },
  {
    label: "通道名称",
    prop: "channel_name",
    minWidth: 140,
    align: "left"
  },
  {
    label: "通道编码",
    prop: "channel_code",
    minWidth: 140,
    align: "left"
  },
  {
    label: "原费率",
    prop: "old_value",
    width: 120,
    formatter: ({ old_value }) => `${old_value}%`
  },
  {
    label: "新费率",
    prop: "new_value",
    width: 120,
    formatter: ({ new_value }) => `${new_value}%`
  },
  {
    label: "备注",
    prop: "remark",
    minWidth: 120
  },
  {
    label: "操作时间",
    prop: "create_time",
    minWidth: 180,
    formatter: ({ create_time }) =>
      dayjs(create_time * 1000).format("YYYY-MM-DD HH:mm:ss")
  }
];

const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

// 获取费率记录列表
const getList = async () => {
  if (!currentChannelId.value) return;

  loading.value = true;
  try {
    const { data } = await getFeeRecordList({
      channel_id: currentChannelId.value,
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      start_time: form.daterange?.[0],
      end_time: form.daterange?.[1]
    });
    dataList.value = data.list;
    pagination.total = data.total;
  } catch (error) {
    message(error.message || "获取费率记录失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  if (!formRef.value) return;
  formRef.value.resetFields();
  pagination.currentPage = 1;
  getList();
};

// 分页大小改变
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  getList();
};

// 当前页改变
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getList();
};

// 打开弹窗
const open = (channelId: number) => {
  currentChannelId.value = channelId;
  dialogVisible.value = true;
  getList();
};

// 监听弹窗显示状态
watch(
  () => dialogVisible.value,
  newVal => {
    emit("update:visible", newVal);
    if (!newVal) {
      emit("close");
    }
  }
);

defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.record-dialog {
  :deep(.el-dialog__body) {
    padding: 10px 20px;
    height: 80vh;
    overflow: hidden;
  }
}

.main {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-form {
  flex-shrink: 0;
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}

:deep(.pure-table) {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .el-table {
    flex: 1;
    overflow: hidden;

    .el-table__inner-wrapper {
      height: 100%;
    }

    .el-table__header-wrapper {
      width: 100% !important;
    }

    .el-table__body-wrapper {
      height: calc(100% - 40px) !important;
      overflow-y: auto;
    }
  }
}
</style>
