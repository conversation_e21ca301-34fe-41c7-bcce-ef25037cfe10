<template>
  <div class="risk-test-page">
    <el-card class="mb-4">
      <template #header>
        <span>风控配置功能测试</span>
      </template>
      
      <div class="test-buttons">
        <el-button type="primary" @click="testChannelRisk">
          测试通道风控配置
        </el-button>
        <el-button type="warning" @click="testPoolRisk">
          测试通道池风控配置
        </el-button>
        <el-button type="success" @click="testMemberRisk">
          测试通道池成员风控配置
        </el-button>
      </div>
    </el-card>

    <!-- 风控配置弹窗 -->
    <el-dialog
      v-model="riskConfigVisible"
      :title="dialogTitle"
      width="80%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <RiskConfig
        v-if="riskConfigVisible"
        ref="riskConfigRef"
        :channelId="testChannelId"
        :source="testSource"
        @success="handleRiskConfigSuccess"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="riskConfigVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveRiskConfig">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { message } from "@/utils/message";
import RiskConfig from "../list/risk-new.vue";

const riskConfigVisible = ref(false);
const riskConfigRef = ref();
const testChannelId = ref(null);
const testSource = ref("channel");
const dialogTitle = ref("风控配置测试");

// 测试通道风控配置
const testChannelRisk = () => {
  testChannelId.value = 1; // 假设通道ID为1
  testSource.value = "channel";
  dialogTitle.value = "通道风控配置测试";
  riskConfigVisible.value = true;
};

// 测试通道池风控配置
const testPoolRisk = () => {
  testChannelId.value = 1; // 假设通道池ID为1
  testSource.value = "pool";
  dialogTitle.value = "通道池风控配置测试";
  riskConfigVisible.value = true;
};

// 测试通道池成员风控配置
const testMemberRisk = () => {
  testChannelId.value = 1; // 假设通道池成员ID为1
  testSource.value = "member";
  dialogTitle.value = "通道池成员风控配置测试";
  riskConfigVisible.value = true;
};

// 保存风控配置
const handleSaveRiskConfig = async () => {
  try {
    await riskConfigRef.value?.onSubmit();
    riskConfigVisible.value = false;
  } catch (error) {
    console.error('保存风控配置失败:', error);
  }
};

// 风控配置保存成功回调
const handleRiskConfigSuccess = () => {
  message("风控配置保存成功", { type: "success" });
  riskConfigVisible.value = false;
};
</script>

<style scoped>
.risk-test-page {
  padding: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.test-buttons {
  display: flex;
  gap: 16px;
}
</style>
