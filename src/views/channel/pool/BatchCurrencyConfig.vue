<template>
  <el-drawer
    v-model="drawerVisible"
    :title="`批量设置币种 - ${poolName}`"
    size="500px"
    :destroy-on-close="false"
    @closed="handleClose"
  >
    <div class="info-box">
      <el-alert
        title="批量币种配置说明"
        type="info"
        :closable="false"
        description="批量设置将应用于所有选中的通道成员。系统已实现级联默认值机制，您可以选择不设置，使用通道池的默认配置。"
        show-icon
      />
    </div>

    <el-form
      ref="formRef"
      :model="formData"
      label-width="120px"
      class="config-form"
    >
      <el-form-item label="通道池">
        <el-tag type="primary">{{ poolName }}</el-tag>
      </el-form-item>

      <el-form-item label="支持币种" prop="currencies">
        <el-select
          v-model="formData.currencies"
          placeholder="请选择支持的币种"
          multiple
          clearable
          collapse-tags
          collapse-tags-tooltip
        >
          <el-option
            v-for="currency in currencyOptions"
            :key="currency.id"
            :label="currency.name"
            :value="currency.id"
          >
            <span>{{ currency.name }} ({{ currency.code }})</span>
          </el-option>
        </el-select>
        <div class="form-tip">
          选择此通道池成员支持的币种。不选择则系统会自动使用通道配置或通道池默认配置。
        </div>
      </el-form-item>

      <el-form-item label="继承配置">
        <el-switch
          v-model="formData.inherit"
          active-text="启用"
          inactive-text="禁用"
          @change="handleInheritChange"
        />
        <div class="form-tip">
          启用后，系统将自动继承通道的币种配置，同时保留通道池的默认币种配置。
        </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" :loading="loading" @click="submitForm">
          批量保存
        </el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, watch } from "vue";
import { message } from "@/utils/message";
import {
  getAllCurrencies,
  getMerchantCurrencies,
  batchUpdatePoolMemberCurrency
} from "@/api/currency";

// 定义props和事件
const props = defineProps({
  pool: {
    type: Object,
    default: () => null
  },
  selectedMembers: {
    type: Array,
    default: () => []
  },
  merchantId: {
    type: [Number, String],
    default: ""
  }
});

const emit = defineEmits(["close", "success"]);

// 控制抽屉显示
const drawerVisible = ref(true); // Start as visible since component is conditionally rendered
const loading = ref(false);

// 定义通道池ID和名称
const poolId = ref<number | null>(null);
const poolName = ref("");

// 表单数据和选项
const formRef = ref(null);
const formData = ref({
  currencies: [] as number[],
  inherit: true,
  member_ids: [] as number[]
});

// 币种选项
const currencyOptions = ref<Array<any>>([]);

// 获取所有币种数据
const fetchAllCurrencies = async () => {
  try {
    const res = await getAllCurrencies();
    if (res && res.code === 200 && res.data) {
      currencyOptions.value = res.data;
    }
  } catch (error) {
    console.error("获取币种数据失败:", error);
  }
};

// 获取商户币种数据
const fetchMerchantCurrencies = async () => {
  try {
    const res = await getMerchantCurrencies({
      merchant_id: props.merchantId
    });

    if (res && res.code === 200 && res.data) {
      // 查看返回的结构，适配不同格式
      if (Array.isArray(res.data)) {
        currencyOptions.value = res.data.map(item => ({
          id: item.currency_id || item.id,
          name: item.name,
          code: item.code
        }));
      } else if (res.data.list && Array.isArray(res.data.list)) {
        currencyOptions.value = res.data.list.map(item => ({
          id: item.currency_id || item.id,
          name: item.name,
          code: item.code
        }));
      }
    }
  } catch (error) {
    console.error("获取商户币种数据失败:", error);
    // 失败时回退到获取所有币种
    fetchAllCurrencies();
  }
};

// 处理继承开关变化
const handleInheritChange = value => {
  if (value) {
    // 提示用户启用了继承
    message("已启用继承配置，系统将自动继承通道的币种配置", { type: "info" });
  }
};

// 重置表单
const resetForm = () => {
  formData.value.currencies = [];
  formData.value.inherit = true;
};

// 提交表单
const submitForm = async () => {
  // 确保member_ids是最新的
  formData.value.member_ids = props.selectedMembers.map(member => member.id);

  if (formData.value.member_ids.length === 0) {
    message("请至少选择一个通道池成员", { type: "warning" });
    return;
  }

  try {
    loading.value = true;

    const params = {
      pool_id: poolId.value,
      member_ids: formData.value.member_ids,
      currencies: formData.value.currencies,
      inherit: formData.value.inherit
    };

    const response = await batchUpdatePoolMemberCurrency(params);

    if (!(response.code === 200 || response.code === 1)) {
      message(response.msg || "批量更新币种配置失败", { type: "error" });
      return;
    }

    message("批量更新币种配置成功", { type: "success" });
    emit("success");
    drawerVisible.value = false;
  } catch (error) {
    console.error("批量更新币种配置时出错:", error);
    message(`批量更新失败: ${error?.message || "未知错误"}`, {
      type: "error"
    });
  } finally {
    loading.value = false;
  }
};

// 关闭抽屉
const handleClose = () => {
  emit("close");
};

// Update form data when selectedMembers changes
watch(
  () => props.selectedMembers,
  newMembers => {
    if (newMembers && newMembers.length > 0) {
      formData.value.member_ids = newMembers.map(member => member.id);
    }
  },
  { deep: true, immediate: true }
);

// 监听pool变化
watch(
  () => props.pool,
  newVal => {
    if (newVal) {

      if (!newVal.id) {
        message("配置错误：通道池缺少ID", { type: "error" });
        return;
      }

      poolId.value = newVal.id;
      poolName.value = newVal.name || `通道池 ${newVal.id}`;

      // 重置表单
      formData.value.currencies = [];

      // 根据是否有商户ID决定获取哪些币种数据
      if (props.merchantId) {
        fetchMerchantCurrencies();
      } else {
        fetchAllCurrencies();
      }
    }
  },
  { immediate: true }
);

// 初始化数据
onMounted(() => {
  // 初始化选中的成员ID
  if (props.selectedMembers && props.selectedMembers.length) {
    formData.value.member_ids = props.selectedMembers.map(member => member.id);
  }
});
</script>

<style scoped>
.config-form {
  padding: 20px;
  max-width: 100%;
}
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
.info-box {
  padding: 20px 20px 0;
}
</style>
