<template>
  <el-dropdown @command="handleExport" :loading="loading">
    <el-button
      type="warning"
      :icon="Download"
      :loading="loading"
      v-bind="$attrs"
    >
      {{ loading ? '导出中...' : '导出' }}
      <el-icon class="el-icon--right">
        <arrow-down />
      </el-icon>
    </el-button>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item command="csv">
          <el-icon><Document /></el-icon>
          CSV (.csv)
        </el-dropdown-item>
        <el-dropdown-item command="xlsx">
          <el-icon><Document /></el-icon>
          Excel (.xlsx)
        </el-dropdown-item>
        <el-dropdown-item command="xls">
          <el-icon><Document /></el-icon>
          Excel (.xls)
        </el-dropdown-item>
        <el-dropdown-item command="txt">
          <el-icon><Document /></el-icon>
          TXT (.txt)
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { Download, ArrowDown, Document } from "@element-plus/icons-vue";
import { message } from "@/utils/message";
import { getToken, formatToken } from "@/utils/auth";

interface Props {
  exportUrl: string;
  params?: Record<string, any>;
  filename?: string;
}

const props = withDefaults(defineProps<Props>(), {
  params: () => ({}),
  filename: ""
});

const loading = ref(false);

const handleExport = async (format: string) => {
  if (loading.value) return;

  loading.value = true;

  try {
    // 构建查询参数
    const queryParams = new URLSearchParams();
    Object.keys(props.params).forEach(key => {
      const value = props.params[key];
      if (value !== null && value !== undefined && value !== '') {
        queryParams.append(key, String(value));
      }
    });

    // 添加格式参数
    queryParams.append('format', format);

    // 构建完整的导出URL
    const baseURL = import.meta.env.VITE_API_BASE_URL || "/merchant";
    const fullUrl = baseURL + props.exportUrl + (queryParams.toString() ? '?' + queryParams.toString() : '');

    // 获取token信息
    const tokenInfo = getToken();
    if (!tokenInfo?.accessToken) {
      throw new Error('用户未登录或token已过期');
    }

    // 使用fetch请求下载文件
    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Authorization': formatToken(tokenInfo.accessToken),
        'Accept': 'application/vnd.ms-excel, text/csv, application/json, text/html, */*'
      }
    });

    if (!response.ok) {
      // 尝试读取错误信息
      let errorMessage = `导出失败: ${response.status} ${response.statusText}`;

      try {
        const errorText = await response.text();
        console.error('导出失败:', errorText);

        // 尝试解析JSON错误信息
        try {
          const errorJson = JSON.parse(errorText);
          if (errorJson.msg) {
            errorMessage = errorJson.msg;
          }
        } catch (e) {
          // 如果不是JSON，使用原始错误文本
          if (errorText) {
            errorMessage = errorText;
          }
        }
      } catch (e) {
        console.error('读取错误信息失败:', e);
      }

      throw new Error(errorMessage);
    }

    // 获取文件内容
    const blob = await response.blob();

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.style.display = 'none';

    // 从响应头获取文件名，如果没有则使用默认文件名
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = props.filename || `export_${Date.now()}`;

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '');
      }
    } else {
      // 如果没有从响应头获取到文件名，根据格式添加扩展名
      const extensions = {
        csv: '.csv',
        xlsx: '.xlsx',
        xls: '.xls',
        txt: '.txt'
      };

      if (!filename.includes('.')) {
        filename += extensions[format] || '.csv';
      }
    }

    link.download = filename;

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    message(`导出${format.toUpperCase()}格式成功`, { type: "success" });

  } catch (error) {
    console.error('导出失败:', error);
    message(error.message || "导出失败，请重试", { type: "error" });
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.el-dropdown {
  margin-left: 8px;
}

.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
