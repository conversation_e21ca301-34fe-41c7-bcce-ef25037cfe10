<!-- src/components/ChannelDirectionSelect.vue -->
<template>
  <el-select
    :model-value="modelValue"
    :placeholder="placeholder"
    :style="{ width }"
    :clearable="clearable"
    @update:modelValue="handleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script setup>
import { defineProps, defineEmits } from "vue";

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: 0
  },
  placeholder: {
    type: String,
    default: "请选择通道类型"
  },
  width: {
    type: String,
    default: "100%"
  },
  clearable: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(["update:modelValue", "change"]);

const options = [
  { label: "全部", value: 0 },
  { label: "代收", value: 1 },
  { label: "代付", value: 2 }
];

const handleChange = val => {
  emit("update:modelValue", val);
  emit("change", val);
};
</script>
