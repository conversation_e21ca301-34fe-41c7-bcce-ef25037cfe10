<template>
  <el-select
    v-model="selectedValue"
    multiple
    collapse-tags
    collapse-tags-tooltip
    :placeholder="placeholder"
    :loading="loading"
    :style="{ width }"
    @change="handleChange"
  >
    <template v-if="filterDirection === 0 || filterDirection === 1">
      <el-option-group label="代收通道">
        <el-option
          v-for="item in options.collection"
          :key="item.id"
          :label="`${item.name} [${item.code}]`"
          :value="item.id"
        >
          <div class="flex justify-between">
            <span>{{ item.name }}</span>
            <span class="text-gray-400 text-xs">{{ item.code }}</span>
          </div>
          <div class="text-xs text-gray-500">
            {{ item.currency_name }} ({{ item.currency_code }})
          </div>
        </el-option>
      </el-option-group>
    </template>

    <template v-if="filterDirection === 0 || filterDirection === 2">
      <el-option-group label="代付通道">
        <el-option
          v-for="item in options.transfer"
          :key="item.id"
          :label="`${item.name} [${item.code}]`"
          :value="item.id"
        >
          <div class="flex justify-between">
            <span>{{ item.name }}</span>
            <span class="text-gray-400 text-xs">{{ item.code }}</span>
          </div>
          <div class="text-xs text-gray-500">
            {{ item.currency_name }} ({{ item.currency_code }})
          </div>
        </el-option>
      </el-option-group>
    </template>
  </el-select>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { getChannelsByMerchant } from "@/api/channel";

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  merchantId: {
    type: [Number, String],
    required: true
  },
  filterDirection: {
    type: Number,
    default: 0 // 0=全部，1=代收，2=代付
  },
  placeholder: {
    type: String,
    default: "请选择通道"
  },
  width: {
    type: String,
    default: "100%"
  }
});

const emit = defineEmits(["update:modelValue", "change"]);

const loading = ref(false);
const options = ref({
  all: [],
  collection: [],
  transfer: []
});
const selectedValue = ref(props.modelValue);

// 根据商户ID加载通道选项
const loadOptions = async () => {
  if (!props.merchantId) {
    console.error("商户ID不能为空");
    return;
  }
  
  loading.value = true;
  try {
    const res = await getChannelsByMerchant(props.merchantId);
    
    if (res && (res.code === 200 || res.code === 1) && res.data) {
      const data = res.data;
      
      // 按方向分组
      const collection = data.filter(item => item.direction === 1);
      const transfer = data.filter(item => item.direction === 2);

      options.value = {
        all: data,
        collection,
        transfer
      };
    }
  } catch (error) {
    console.error("获取商户通道失败", error);
  } finally {
    loading.value = false;
  }
};

// 处理选择变化
const handleChange = val => {
  emit("update:modelValue", val);
  emit("change", val);
};

// 监听商户ID变化
watch(
  () => props.merchantId,
  newVal => {
    if (newVal) {
      loadOptions();
    }
  }
);

// 监听传入值变化
watch(
  () => props.modelValue,
  newVal => {
    selectedValue.value = newVal;
  }
);

onMounted(() => {
  if (props.merchantId) {
    loadOptions();
  }
});
</script> 