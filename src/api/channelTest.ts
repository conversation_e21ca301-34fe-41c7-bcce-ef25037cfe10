import { http } from "@/utils/http";

/**
 * 测试通道连通性
 * @param data 测试数据
 */
export const testChannelConnectivity = (data: any) => {
  return http.request<any>("post", "/PayChannelTest/connectivity", {
    data
  });
};

/**
 * 测试通道代收功能
 * @param data 测试数据，包含 member_id, amount, currency 等
 */
export const testChannelPayment = (data: any) => {
  return http.request<any>("post", "/PayChannelTest/payment", {
    data
  });
};

/**
 * 测试通道代付功能
 * @param data 测试数据，包含 member_id, amount, account 等
 */
export const testChannelTransfer = (data: any) => {
  return http.request<any>("post", "/PayChannelTest/transfer", {
    data
  });
};

/**
 * 测试通道查询功能
 * @param data 测试数据，包含 member_id, orderNo 等
 */
export const testChannelQuery = (data: any) => {
  return http.request<any>("post", "/PayChannelTest/query", {
    data
  });
};

/**
 * 获取通道测试历史记录
 * @param params 查询参数
 */
export const getChannelTestHistory = (params: any) => {
  return http.request<any>("get", "/PayChannelTest/history", {
    params
  });
};

/**
 * 获取通道测试统计信息
 * @param params 查询参数
 */
export const getChannelTestStats = (params: any) => {
  return http.request<any>("get", "/PayChannelTest/stats", {
    params
  });
};

/**
 * 批量测试通道
 * @param data 批量测试数据
 */
export const batchTestChannels = (data: any) => {
  return http.request<any>("post", "/PayChannelTest/batch", {
    data
  });
};

/**
 * 删除测试记录
 * @param id 记录ID
 */
export const deleteTestRecord = (id: number) => {
  return http.request<any>("delete", `/PayChannelTest/delete/${id}`);
};

/**
 * 导出测试报告
 * @param params 导出参数
 */
export const exportTestReport = (params: any) => {
  return http.request<any>("get", "/PayChannelTest/export", {
    params,
    responseType: 'blob'
  });
};
