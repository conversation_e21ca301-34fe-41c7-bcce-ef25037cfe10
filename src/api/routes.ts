import { http } from "@/utils/http";

type Result = {
  code: number;
  msg: string;
  data: Array<{
    path: string;
    component: string;
    redirect?: string;
    alwaysShow?: boolean;
    name: string;
    meta: {
      title: string;
      icon?: string;
      noCache?: boolean;
      roles: string[];
    };
    hidden?: boolean;
    children?: Array<any>;
  }>;
};

export const getAsyncRoutes = () => {
  return http.request<Result>("get", "/menu/userMenu");
};
