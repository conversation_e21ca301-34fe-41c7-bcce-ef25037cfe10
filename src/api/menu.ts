import { http } from "@/utils/http";

export type MenuResult = {
  code: number;
  data: Array<MenuType>;
  message: string;
};

export type MenuType = {
  id: number;
  pid: number;
  type: number;
  name: string;
  path: string;
  component: string;
  redirect: string;
  permission: string;
  title: string;
  icon: string;
  sort: number;
  status: number;
  is_cache: number;
  is_show: number;
  children?: MenuType[];
};

// 菜单查询参数
export interface MenuQuery {
  name?: string;
  status?: number;
}

// 菜单列表接口返回
export interface MenuListResult {
  code: number;
  data: MenuType[];
  message: string;
}

// 获取菜单列表
export const getMenuList = (data?: MenuQuery) => {
  return http.request<MenuListResult>("get", "/menu/list", { data });
};

// 添加菜单
export const addMenu = (data: MenuType) => {
  return http.request<any>("post", "/menu/add", { data });
};

// 修改菜单
export const updateMenu = (data: MenuType) => {
  return http.request<any>("put", "/menu/edit", { data });
};

// 修改菜单状态
export const updateMenuStatus = (data: { id: number; status: number }) => {
  return http.request<any>("put", "/menu/status", { data });
};

// 删除菜单
export const deleteMenu = (id: string | number) => {
  return http.request<MenuResult>("delete", "/menu/delete", {
    params: { ids: id }
  });
};

// 获取用户菜单
export const getUserMenus = () => {
  return http.request<MenuResult>("get", "/menu/userMenu");
};

// 获取用户权限
export const getUserPermissions = () => {
  return http.request<MenuResult>("get", "/menu/permissions");
};
