import { http } from "@/utils/http";

/** 获取绑定列表 */
export const getBotBindList = (params?: any) => {
  return http.request<any>("get", "/botbind/list", { params });
};

/** 添加绑定 */
export const addBotBind = (data?: any) => {
  return http.request<any>("post", "/botbind/add", { data });
};

/** 编辑绑定 */
export const editBotBind = (data?: any) => {
  return http.request<any>("put", "/botbind/edit", { data });
};

/** 删除绑定 */
export const deleteBotBind = (id: number) => {
  return http.request<any>("delete", `/botbind/delete/${id}`);
};

/** 用户类型枚举 */
export enum UserType {
  Merchant = 1, // 商户
  CodeMerchant = 2 // 码商
}
