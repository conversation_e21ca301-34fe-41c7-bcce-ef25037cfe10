import { http } from "@/utils/http";

// 获取提现列表
export const getWithdrawList = (params?: object) => {
  return http.request<any>("get", "/MerchantWithdraw/list", { params });
};

// 获取提现详情
export const getWithdrawDetail = (id: number) => {
  return http.request<any>("get", `/MerchantWithdraw/detail/${id}`);
};

// 新增提现
export const addWithdraw = (data: object) => {
  return http.request<any>("post", "/withdraw/add", { data });
};

// 编辑提现
export const editWithdraw = (data: object) => {
  return http.request<any>("put", "/withdraw/edit", { data });
};

// 删除提现
export const deleteWithdraw = (data: { id: string }) => {
  return http.request<any>("delete", "/withdraw/delete", { data });
};

// 修改提现状态
export const updateWithdrawStatus = (data: object) => {
  return http.request<any>("put", "/withdraw/status", { data });
};

// 审核提现
export const auditWithdraw = (data: object) => {
  return http.request<any>("post", "/MerchantWithdraw/audit", { data });
};

// 提现退回
export const withdrawBack = (data: object) => {
  return http.request<any>("put", "/withdraw/back", { data });
};

// 提现打款
export const withdrawPay = (data: object) => {
  return http.request<any>("put", "/withdraw/pay", { data });
};

// 提现退款
export const withdrawRefund = (data: object) => {
  return http.request<any>("put", "/withdraw/refund", { data });
};

// 获取提现统计
export const getWithdrawStatistics = (params?: object) => {
  return http.request<any>("get", "/MerchantWithdraw/statistics", { params });
};

// 导出提现记录
export const exportWithdraw = (params?: object) => {
  return http.request<any>("get", "/MerchantWithdraw/export", { params });
};

// 提现方式类型
export const withdrawTypeMap = {
  1: "银行卡",
  2: "支付宝",
  3: "微信",
  4: "USDT"
} as const;

// 提现记录状态类型
export const withdrawStatusMap = {
  0: "待处理",
  1: "成功",
  2: "失败",
  3: "处理中"
} as const;

// 提现记录状态标签类型
export const withdrawStatusTagType = {
  0: "warning",
  1: "success",
  2: "danger"
} as const;

// 提现记录接口类型定义
export interface WithdrawRecord {
  id: number;
  merchant_id: number;
  merchant_name: string;
  order_no: string;
  amount: number;
  fee: number;
  real_amount: number;
  withdraw_type: 1 | 2 | 3 | 4;
  bank_name: string;
  bank_card: string;
  bank_owner: string;
  bank_branch: string;
  alipay_account: string;
  wechat_account: string;
  usdt_address: string;
  usdt_type: string;
  status: 0 | 1 | 2 | 3;
  fail_reason: string;
  admin_id: number;
  admin_name: string;
  process_time: number;
  remark: string;
  create_time: number;
  update_time: number;
}

// 提现统计接口类型定义
export interface WithdrawStatistics {
  total_amount: string;
  total_fee: string;
  total_real_amount: string;
  status_count: {
    pending: number;
    success: number;
    failed: number;
    processing: number;
  };
  type_amount: {
    bank: string;
    alipay: string;
    wechat: string;
    usdt: string;
  };
}
