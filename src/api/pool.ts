import { http } from "@/utils/http";

// 获取通道池列表
export const getPoolList = (params?: object) => {
  return http.request("get", "/PayChannelPool/list", { params });
};

// 添加通道池
export const addPool = (data: object) => {
  return http.request("post", "/PayChannelPool/add", { data });
};

// 更新通道池
export const updatePool = (data: object) => {
  return http.request("post", "/PayChannelPool/edit", { data });
};

// 删除通道池
export const deletePool = (id: number) => {
  return http.request("post", "/PayChannelPool/delete", { data: { id } });
};

// 获取通道池详情
export const getPoolDetail = (id: number) => {
  return http.request("get", "/PayChannelPool/info", { params: { id } });
};

// 更新通道池状态
export const updatePoolStatus = (data: object) => {
  return http.request("post", "/PayChannelPool/status", { data });
};

// 获取通道池风控配置
export const getPoolRiskConfig = (params: any) => {
  return http.request("get", "/PayChannelPool/getRiskConfig", { params });
};

// 保存通道池风控配置
export const savePoolRiskConfig = (data: object) => {
  return http.request("post", "/PayChannelPool/saveRiskConfig", { data });
};
