// src/api/monitor.ts
import { http } from "@/utils/http";

// 获取队列列表
export const getQueueList = (params?: object) => {
  return http.request("get", "/queue/tasks", { params });
};

// 执行任务
export const executeTask = (id: number) => {
  return http.request("get", `/queue/executeTask/${id}`);
};

// 修改任务状态
export const updateTaskStatus = (data: object) => {
  return http.request("put", "/queue/status", { data });
};

// 获取任务日志
export const getTaskLogs = (params?: object) => {
  return http.request("get", "/queue/messages", { params });
};

// 清除任务日志
export const clearTaskLogs = (days: number) => {
  return http.request("get", `/queue/clearLog?days=${days}`);
};

// 获取Supervisor状态
export const getSupervisorStatus = () => {
  return http.request("get", "/queue/supervisorStatus");
};

// 重启Supervisor进程
export const restartSupervisor = (process: string) => {
  return http.request("get", `/queue/restartSupervisor/${process}`);
};
