# 订单过期HTTP状态码修改方案

## 🎯 **修改目标**

让过期的支付链接直接返回HTTP状态码404或500，而不是显示自定义错误页面，确保过期链接完全无法访问。

## 📝 **修改文件**

### **主要修改文件**
- `app/api/controller/Cashier.php` - 收银台控制器

## 🔧 **具体修改内容**

### **1. 添加订单过期检查方法**

```php
/**
 * 检查订单是否过期
 */
private function isOrderExpired($order): bool
{
    // 检查订单状态是否为过期
    if ($order->status == Order::STATUS_EXPIRED) {
        return true;
    }
    
    // 检查过期时间
    if ($order->expire_time && $order->expire_time < time()) {
        return true;
    }
    
    return false;
}
```

### **2. 修改 info() 方法**

**修改前：**
```php
if (empty($token)) {
    return $this->error('Token参数不能为空');
}

if (!$order) {
    return $this->error('订单不存在');
}
```

**修改后：**
```php
if (empty($token)) {
    // Token为空，返回404
    return response('Not Found', 404);
}

if (!$order) {
    // 订单不存在，返回404
    return response('Not Found', 404);
}

// 检查订单是否过期
if ($this->isOrderExpired($order)) {
    // 订单过期，返回404
    return response('Not Found', 404);
}
```

### **3. 修改 checkStatus() 方法**

**修改前：**
```php
if (empty($orderNo)) {
    return $this->error('订单号不能为空');
}

if (!$order) {
    return $this->error('订单不存在');
}
```

**修改后：**
```php
if (empty($orderNo)) {
    // 订单号为空，返回404
    return response('Not Found', 404);
}

if (!$order) {
    // 订单不存在，返回404
    return response('Not Found', 404);
}

// 检查订单是否过期
if ($this->isOrderExpired($order)) {
    // 订单过期，返回404
    return response('Not Found', 404);
}
```

### **4. 修改 status() 方法**

**修改前：**
```php
if (count($pathParts) < 4 || empty($pathParts[3])) {
    return $this->error('Token参数缺失，路径：' . $pathInfo);
}

if (!$tokenData || !isset($tokenData[0])) {
    return $this->error('Token无效');
}

if (!$order) {
    return $this->error('订单不存在');
}
```

**修改后：**
```php
if (count($pathParts) < 4 || empty($pathParts[3])) {
    // Token参数缺失，返回404
    return response('Not Found', 404);
}

if (!$tokenData || !isset($tokenData[0])) {
    // Token无效，返回404
    return response('Not Found', 404);
}

if (!$order) {
    // 订单不存在，返回404
    return response('Not Found', 404);
}

// 检查订单是否过期
if ($this->isOrderExpired($order)) {
    // 订单过期，返回404
    return response('Not Found', 404);
}
```

### **5. 修改 checkPaymentStatus() 方法**

**修改前：**
```php
if (!$order) {
    return $this->error('订单不存在');
}

catch (\Exception $e) {
    return $this->error('查询状态失败：' . $e->getMessage());
}
```

**修改后：**
```php
if (!$order) {
    // 订单不存在，返回404
    return response('Not Found', 404);
}

// 检查订单是否过期
if ($this->isOrderExpired($order)) {
    // 订单过期，返回404
    return response('Not Found', 404);
}

catch (PaymentException $e) {
    // Token过期或解析失败，返回404
    if (strpos($e->getMessage(), '过期') !== false) {
        return response('Not Found', 404);
    }
    return response('Not Found', 404);
} catch (\Exception $e) {
    // 其他异常，返回500
    return response('Internal Server Error', 500);
}
```

### **6. 修复方法调用错误**

**修改前：**
```php
$tokenData = $this->decryptToken($token);
```

**修改后：**
```php
$tokenData = $this->decryptTokenData($token);
```

## 📊 **修改效果对比**

### **修改前的行为**
| 场景 | 返回状态 | 响应内容 | 前端表现 |
|------|----------|----------|----------|
| 正常订单 | HTTP 200 | JSON数据 | 正常显示支付页面 |
| 过期订单 | HTTP 200 | JSON错误信息 | 显示自定义过期页面 |
| 不存在订单 | HTTP 200 | JSON错误信息 | 显示自定义错误页面 |
| Token无效 | HTTP 200 | JSON错误信息 | 显示自定义错误页面 |

### **修改后的行为**
| 场景 | 返回状态 | 响应内容 | 前端表现 |
|------|----------|----------|----------|
| 正常订单 | HTTP 200 | JSON数据 | 正常显示支付页面 |
| 过期订单 | **HTTP 404** | **"Not Found"** | **浏览器404错误页面** |
| 不存在订单 | **HTTP 404** | **"Not Found"** | **浏览器404错误页面** |
| Token无效 | **HTTP 404** | **"Not Found"** | **浏览器404错误页面** |
| 系统异常 | **HTTP 500** | **"Internal Server Error"** | **浏览器500错误页面** |

## 🎯 **涉及的API接口**

1. **`/api/cashier/info/{token}`** - 获取支付信息
2. **`/api/cashier/checkStatus`** - 查询支付状态（使用订单号）
3. **`/api/cashier/status/{token}`** - 查询支付状态（使用token）
4. **`/api/cashier/checkPaymentStatus/{token}`** - 查询支付状态

## 🔍 **过期检查逻辑**

### **两种过期判断方式**
1. **状态过期**：`$order->status == Order::STATUS_EXPIRED` (-2)
2. **时间过期**：`$order->expire_time < time()`

### **过期检查流程**
```php
private function isOrderExpired($order): bool
{
    // 1. 检查订单状态
    if ($order->status == Order::STATUS_EXPIRED) {
        return true;
    }
    
    // 2. 检查过期时间
    if ($order->expire_time && $order->expire_time < time()) {
        return true;
    }
    
    return false;
}
```

## 🚀 **部署和测试**

### **1. 部署步骤**
1. 备份原文件：`cp app/api/controller/Cashier.php app/api/controller/Cashier.php.bak`
2. 应用修改后的文件
3. 重启PHP-FPM或相关服务
4. 清理缓存：`php think clear`

### **2. 测试方法**
```bash
# 测试正常订单
curl -i "http://your-domain/api/cashier/info/valid_token"

# 测试过期订单
curl -i "http://your-domain/api/cashier/info/expired_token"

# 测试不存在订单
curl -i -X POST "http://your-domain/api/cashier/checkStatus" \
     -H "Content-Type: application/json" \
     -d '{"order_no":"NONEXISTENT"}'
```

### **3. 预期结果**
- 正常订单：返回 `HTTP/1.1 200 OK`
- 过期订单：返回 `HTTP/1.1 404 Not Found`
- 不存在订单：返回 `HTTP/1.1 404 Not Found`
- 系统异常：返回 `HTTP/1.1 500 Internal Server Error`

## ⚠️ **注意事项**

### **1. 前端影响**
- 前端收到404/500状态码后，浏览器会显示标准错误页面
- 用户无法看到任何支付相关内容
- 实现了完全阻止访问过期链接的效果

### **2. 日志记录**
建议在返回404/500前添加日志记录：
```php
\think\facade\Log::info('Order expired access attempt', [
    'order_no' => $order->order_no ?? 'unknown',
    'token' => substr($token, 0, 10) . '...',
    'ip' => request()->ip()
]);
```

### **3. 监控建议**
- 监控404错误的频率，了解过期链接访问情况
- 设置告警，及时发现异常情况
- 定期清理过期订单数据

## ✅ **验证清单**

- [ ] 正常订单可以正常访问
- [ ] 过期订单返回404状态码
- [ ] 不存在订单返回404状态码
- [ ] Token无效返回404状态码
- [ ] 系统异常返回500状态码
- [ ] 前端显示浏览器标准错误页面
- [ ] 日志记录正常
- [ ] 性能无明显影响

## 🎉 **预期效果**

修改完成后，过期的支付链接将：
- ✅ 直接返回HTTP 404状态码
- ✅ 浏览器显示"页面未找到"错误
- ✅ 用户完全无法访问过期内容
- ✅ 实现了您要求的效果

这样既简单又有效，过期链接完全无法访问，符合您的需求！
