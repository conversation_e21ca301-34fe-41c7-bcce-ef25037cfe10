<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaymentAPI - Enterprise Payment Gateway Documentation</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #0969da;
            --primary-hover: #0860ca;
            --secondary-color: #6f42c1;
            --accent-color: #d73a49;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --info-color: #17a2b8;

            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: #e9ecef;
            --bg-code: #f6f8fa;

            --text-primary: #24292f;
            --text-secondary: #656d76;
            --text-muted: #8b949e;
            --text-link: #0969da;

            --border-color: #d1d9e0;
            --border-muted: #e1e4e8;

            --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Noto Sans', Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--bg-primary);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* 清爽的背景纹理 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                linear-gradient(90deg, transparent 79px, rgba(0,0,0,0.02) 81px, transparent 82px),
                linear-gradient(rgba(0,0,0,0.02) 0.5px, transparent 0.5px);
            background-size: 80px 80px;
            z-index: -1;
            opacity: 0.3;
        }

        .app-layout {
            display: flex;
            min-height: 100vh;
        }

        /* 左侧边栏 */
        .sidebar {
            width: 280px;
            background: var(--bg-primary);
            border-right: 1px solid var(--border-color);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            overflow-y: auto;
            z-index: 100;
        }

        .sidebar-header {
            padding: 24px 20px;
            border-bottom: 1px solid var(--border-color);
            background: var(--bg-secondary);
        }

        .sidebar-header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .sidebar-header h1 i {
            color: var(--primary-color);
        }

        .sidebar-header p {
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin: 0 0 12px 0;
        }

        .version-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            background: var(--primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        /* 导航菜单 */
        .nav-menu {
            padding: 20px 0;
        }

        .nav-item {
            display: block;
            padding: 12px 20px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav-item:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }

        .nav-item.active {
            background: var(--primary-color);
            color: white;
            border-right: 3px solid var(--primary-color);
        }

        .nav-item i {
            width: 16px;
            text-align: center;
            font-size: 0.875rem;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 280px;
            background: var(--bg-primary);
            display: flex;
        }

        .content-wrapper {
            flex: 1;
            max-width: calc(100% - 240px);
            margin: 0 auto;
            padding: 0 20px;
        }

        .content-header {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            padding: 20px 40px;
            position: sticky;
            top: 0;
            z-index: 50;
            max-width: 1200px;
            margin: 0 auto;
            box-sizing: border-box;
        }

        .content-header h1 {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .content-body {
            padding: 32px 40px;
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
            box-sizing: border-box;
        }

        /* 右侧目录索引 */
        .toc-sidebar {
            width: 240px;
            background: var(--bg-secondary);
            border-left: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            height: 100vh;
            overflow-y: auto;
            padding: 20px 0;
        }

        .toc-title {
            padding: 0 20px 12px 20px;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 16px;
        }

        .toc-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .toc-item {
            margin: 0;
        }

        .toc-link {
            display: block;
            padding: 6px 20px;
            color: var(--text-secondary);
            text-decoration: none;
            font-size: 0.8125rem;
            line-height: 1.4;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .toc-link:hover {
            color: var(--text-primary);
            background: var(--bg-primary);
        }

        .toc-link.active {
            color: var(--primary-color);
            background: var(--bg-primary);
            border-left-color: var(--primary-color);
        }

        .toc-link.level-2 {
            padding-left: 32px;
            font-size: 0.75rem;
        }

        .toc-link.level-3 {
            padding-left: 44px;
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        /* 移动端汉堡菜单 */
        .mobile-menu-toggle {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 200;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 6px;
            cursor: pointer;
        }



        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            font-size: 1.75rem;
            color: var(--text-primary);
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
        }

        .section h2 i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        .section h3 {
            font-size: 1.25rem;
            color: var(--text-primary);
            margin: 28px 0 16px 0;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .section h3 i {
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .api-endpoint {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-left: 4px solid var(--primary-color);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }

        .api-endpoint h3 {
            margin-top: 0;
            margin-bottom: 8px;
            font-size: 1.125rem;
        }

        .api-endpoint p {
            color: var(--text-secondary);
            margin: 0;
            font-size: 0.9rem;
        }

        .method {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            margin-right: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .method.post {
            background: var(--success-color);
            color: white;
        }
        .method.get {
            background: var(--primary-color);
            color: white;
        }

        .code-block {
            background: var(--bg-code);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
            padding: 20px;
            border-radius: 6px;
            margin: 16px 0;
            overflow-x: auto;
            position: relative;
        }

        .code-block code {
            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
            color: var(--text-primary);
            display: block;
            white-space: pre-wrap;
            word-wrap: break-word;
            margin: 0;
            padding: 0;
            background: none;
        }

        .copy-btn {
            position: absolute;
            top: 12px;
            right: 12px;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.75rem;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .copy-btn:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
        }

        .copy-btn:active {
            transform: translateY(0);
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
        }

        .param-table th,
        .param-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid var(--border-muted);
            font-size: 0.875rem;
        }

        .param-table th {
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.8125rem;
        }

        .param-table td {
            color: var(--text-secondary);
        }

        .param-table tr:hover {
            background: var(--bg-secondary);
        }

        .param-table tr:last-child td {
            border-bottom: none;
        }

        .param-table code {
            background: var(--bg-code);
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.8125rem;
            color: var(--text-primary);
        }

        .required {
            color: var(--accent-color);
            font-weight: 600;
            font-size: 0.75rem;
        }

        .optional {
            color: var(--text-muted);
            font-weight: 500;
            font-size: 0.75rem;
        }

        .alert {
            padding: 16px 20px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid;
            background: var(--bg-secondary);
        }

        .alert-info {
            border-left-color: var(--info-color);
            background: #f0f9ff;
            color: #0c4a6e;
        }

        .alert-warning {
            border-left-color: var(--warning-color);
            background: #fffbeb;
            color: #92400e;
        }

        .alert-success {
            border-left-color: var(--success-color);
            background: #f0fdf4;
            color: #166534;
        }

        .alert strong {
            font-weight: 600;
        }

        .status-code {
            display: inline-flex;
            align-items: center;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
            font-size: 0.8125rem;
            font-weight: 600;
        }

        .status-200 {
            background: #dcfce7;
            color: #166534;
        }
        .status-400 {
            background: #fef2f2;
            color: #dc2626;
        }
        .status-500 {
            background: #fef2f2;
            color: #dc2626;
        }

        .footer {
            text-align: center;
            padding: 48px 40px;
            color: var(--text-secondary);
            margin-top: 64px;
            margin-left: -40px;
            margin-right: -40px;
            border-top: 1px solid var(--border-color);
            background: var(--bg-secondary);
            width: calc(100% + 80px);
            clear: both;
            box-sizing: border-box;
            position: relative;
        }

        .footer p {
            margin-bottom: 8px;
            font-size: 0.875rem;
        }

        .footer a {
            color: var(--text-link);
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--bg-secondary);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-muted);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .toc-sidebar {
                display: none;
            }

            .content-wrapper {
                max-width: 100%;
                padding: 0 40px;
            }

            .footer {
                margin-left: -40px;
                margin-right: -40px;
                width: calc(100% + 80px);
            }
        }

        @media (max-width: 1024px) {
            .sidebar {
                width: 260px;
            }

            .main-content {
                margin-left: 260px;
            }

            .content-body {
                padding: 24px 32px;
            }
        }

        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: block;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-header {
                padding: 20px 24px;
                padding-left: 60px;
            }

            .content-body {
                padding: 20px 24px;
            }

            .content-wrapper {
                padding: 0 20px;
            }

            .footer {
                margin-left: -24px;
                margin-right: -24px;
                width: calc(100% + 48px);
                padding: 32px 24px;
            }

            .section h2 {
                font-size: 1.5rem;
            }

            .section h3 {
                font-size: 1.125rem;
            }

            .param-table {
                font-size: 0.8125rem;
            }

            .param-table th,
            .param-table td {
                padding: 10px 12px;
            }

            .code-block {
                padding: 16px;
                font-size: 0.8125rem;
            }

            .copy-btn {
                position: static;
                margin-bottom: 12px;
                align-self: flex-start;
            }
        }

        @media (max-width: 480px) {
            .content-header h1 {
                font-size: 1.5rem;
            }

            .content-body {
                padding: 16px 20px;
            }

            .content-wrapper {
                padding: 0 16px;
            }

            .param-table th,
            .param-table td {
                padding: 8px 10px;
            }

            .footer {
                margin-left: -20px;
                margin-right: -20px;
                width: calc(100% + 40px);
                padding: 32px 20px;
                margin-top: 32px;
            }

            .content-header {
                padding: 16px 20px;
                padding-left: 60px;
            }
        }

        /* 强制修复所有内容的缩进问题 */
        .content-section {
            margin-left: 0 !important;
            padding-left: 0 !important;
        }

        .content-section .section {
            margin-left: 0 !important;
            padding-left: 0 !important;
        }

        .content-section .section > * {
            margin-left: 0 !important;
            padding-left: 0 !important;
        }

        /* 特别针对问题元素 */
        .content-section table,
        .content-section .code-block,
        .content-section .alert,
        .content-section .api-endpoint,
        .content-section h2,
        .content-section h3,
        .content-section p,
        .content-section div {
            margin-left: 0 !important;
            padding-left: 0 !important;
        }

        /* 恢复必要的内边距 */
        .content-section table th,
        .content-section table td {
            padding: 12px 16px !important;
        }

        .content-section .code-block {
            padding: 20px !important;
        }

        .content-section .alert {
            padding: 16px 20px !important;
        }

        .content-section .api-endpoint {
            padding: 20px !important;
        }

        .content-section ul,
        .content-section ol {
            padding-left: 20px !important;
        }

        /* 打印样式 */
        @media print {
            body {
                background: white;
                color: black;
            }

            .nav-tabs,
            .copy-btn {
                display: none;
            }

            .tab-content {
                display: block !important;
                background: white;
                border: none;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- 移动端菜单按钮 -->
        <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
            <i class="fas fa-bars"></i>
        </button>

        <!-- 左侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-rocket"></i> 支付网关API</h1>
                <p>支付接口文档</p>
                <span class="version-badge">
                    <i class="fas fa-code-branch"></i>
                    v2.0.1
                </span>
            </div>

            <nav class="nav-menu">
                <button class="nav-item active" onclick="showSection('overview')">
                    <i class="fas fa-chart-line"></i> 接口概述
                </button>
                <button class="nav-item" onclick="showSection('auth')">
                    <i class="fas fa-shield-alt"></i> 接口认证
                </button>
                <button class="nav-item" onclick="showSection('collection')">
                    <i class="fas fa-download"></i> 代收接口
                </button>
                <button class="nav-item" onclick="showSection('transfer')">
                    <i class="fas fa-upload"></i> 代付接口
                </button>
                <button class="nav-item" onclick="showSection('order-query')">
                    <i class="fas fa-search"></i> 订单查询
                </button>
                <button class="nav-item" onclick="showSection('balance-query')">
                    <i class="fas fa-wallet"></i> 余额查询
                </button>
                <button class="nav-item" onclick="showSection('collection-callback')">
                    <i class="fas fa-bell"></i> 代收回调
                </button>
                <button class="nav-item" onclick="showSection('transfer-callback')">
                    <i class="fas fa-bell-slash"></i> 代付回调
                </button>
                <button class="nav-item" onclick="showSection('order-receipt')">
                    <i class="fas fa-receipt"></i> 订单凭证
                </button>
                <button class="nav-item" onclick="showSection('examples')">
                    <i class="fas fa-terminal"></i> 示例代码
                </button>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="content-wrapper">
                <div class="content-header">
                    <h1 id="content-title">接口概述</h1>
                </div>

                <div class="content-body">

                <!-- 内容区域 -->
                    <div id="overview" class="content-section active">
                        <div class="section">
                            <h2><i class="fas fa-rocket"></i> 接口概述</h2>
                            <div class="alert alert-info">
                                <strong><i class="fas fa-info-circle"></i> 欢迎使用支付网关API！</strong>
                                本文档提供了完整的支付网关集成指南，包括代收、代付、查询接口和回调通知等功能。
                            </div>

                            <h3><i class="fas fa-globe"></i> 接口域名</h3>
                            <div class="code-block">
                                <button class="copy-btn" onclick="copyToClipboard(this)">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                                <code>
# 生产环境
https://api.paymentgateway.com

# 测试环境
https://sandbox-api.paymentgateway.com
                                </code>
                            </div>

                            <h3><i class="fas fa-cogs"></i> 技术规范</h3>
                            <ul style="margin: 16px 0; padding-left: 20px; color: var(--text-secondary);">
                                <li><strong>协议：</strong> HTTPS/TLS 1.1</li>
                                <li><strong>编码：</strong> UTF-8</li>
                                <li><strong>数据格式：</strong> JSON</li>
                                <li><strong>签名算法：</strong> MD5</li>
                                <li><strong>超时时间：</strong> 30秒</li>
                                <li><strong>频率限制：</strong> 1000次/分钟</li>
                            </ul>

                            <h3><i class="fas fa-sitemap"></i> 接口列表</h3>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th><i class="fas fa-tag"></i> 接口名称</th>
                                        <th><i class="fas fa-exchange-alt"></i> 请求方式</th>
                                        <th><i class="fas fa-link"></i> 接口地址</th>
                                        <th><i class="fas fa-info"></i> 功能说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><strong>代收下单</strong></td>
                                        <td><span class="method post">POST</span></td>
                                        <td><code>/pay/unifiedOrder</code></td>
                                        <td>创建代收订单</td>
                                    </tr>
                                    <tr>
                                        <td><strong>代付下单</strong></td>
                                        <td><span class="method post">POST</span></td>
                                        <td><code>/repay/unifiedOrder</code></td>
                                        <td>创建代付订单</td>
                                    </tr>
                                    <tr>
                                        <td><strong>代收订单查询</strong></td>
                                        <td><span class="method post">POST</span></td>
                                        <td><code>/pay/query</code></td>
                                        <td>查询订单状态</td>
                                    </tr>
                                    <tr>
                                        <td><strong>代付订单查询</strong></td>
                                        <td><span class="method post">POST</span></td>
                                        <td><code>/repay/query</code></td>
                                        <td>查询订单状态</td>
                                    </tr>
                                    <tr>
                                        <td><strong>余额查询</strong></td>
                                        <td><span class="method post">POST</span></td>
                                        <td><code>/query/balance</code></td>
                                        <td>查询商户余额</td>
                                    </tr>
                                </tbody>
                            </table>

                            <h3><i class="fas fa-info-circle"></i> 服务水平</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 16px; margin: 20px 0;">
                                <div style="background: var(--bg-secondary); padding: 16px; border-radius: 6px; border: 1px solid var(--border-color); text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--success-color); margin-bottom: 4px;">99.9%</div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary);">可用性保障</div>
                                </div>
                                <div style="background: var(--bg-secondary); padding: 16px; border-radius: 6px; border: 1px solid var(--border-color); text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--primary-color); margin-bottom: 4px;">&lt;200ms</div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary);">响应时间</div>
                                </div>
                                <div style="background: var(--bg-secondary); padding: 16px; border-radius: 6px; border: 1px solid var(--border-color); text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--info-color); margin-bottom: 4px;">24/7</div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary);">技术支持</div>
                                </div>
                                <div style="background: var(--bg-secondary); padding: 16px; border-radius: 6px; border: 1px solid var(--border-color); text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 600; color: var(--warning-color); margin-bottom: 4px;">150+</div>
                                    <div style="font-size: 0.875rem; color: var(--text-secondary);">覆盖国家</div>
                                </div>
                            </div>
                        </div>
            </div>
                </div>

                <div id="collection" class="content-section">
            <div class="section">
                <h2><i class="fas fa-arrow-down"></i> 代收接口</h2>

                <div class="api-endpoint">
                    <h3><span class="method post">POST</span> /pay/unifiedOrder</h3>
                    <p>创建代收订单，用户支付后资金进入商户账户</p>
                </div>

                <h3><i class="fas fa-list"></i> 请求参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>merchant_id</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户号</td>
                            <td>M2501082278</td>
                        </tr>
                        <tr>
                            <td>amount</td>
                            <td>Float</td>
                            <td><span class="required">是</span></td>
                            <td>订单金额</td>
                            <td>100</td>
                        </tr>
                        <tr>
                            <td>currency</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>货币类型</td>
                            <td>BRL</td>
                        </tr>
                        <tr>
                            <td>channel_code</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>支付通道编码</td>
                            <td>bax</td>
                        </tr>
                        <tr>
                            <td>notify_url</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>异步通知地址</td>
                            <td>http://apis.nrny.com/index/test/notify</td>
                        </tr>
                        <tr>
                            <td>return_url</td>
                            <td>String</td>
                            <td><span class="optional">否</span></td>
                            <td>同步跳转地址</td>
                            <td>http://apis.nrny.com/index/test/callbk</td>
                        </tr>
                        <tr>
                            <td>subject</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商品描述</td>
                            <td>测试商品</td>
                        </tr>
                        <tr>
                            <td>merchant_order_no</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户订单号，唯一</td>
                            <td>TEST1755704243488</td>
                        </tr>
                        <tr>
                            <td>timestamp</td>
                            <td>Integer</td>
                            <td><span class="required">是</span></td>
                            <td>时间戳</td>
                            <td>1755704243</td>
                        </tr>
                        <tr>
                            <td>sign_type</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名类型</td>
                            <td>md5</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名</td>
                            <td>A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-code"></i> 请求示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>POST /pay/unifiedOrder HTTP/1.1
Host: api.yourpay.com
Content-Type: application/json

{
    "merchant_id": "M2501082278",
    "amount": 100,
    "currency": "BRL",
    "channel_code": "bax",
    "notify_url": "http://apis.nrny.com/index/test/notify",
    "return_url": "http://apis.nrny.com/index/test/callbk",
    "subject": "测试商品",
    "merchant_order_no": "TEST1755704243488",
    "timestamp": 1755704243,
    "sign_type": "md5",
    "sign": "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6"
}</code>
                </div>

                <h3><i class="fas fa-check-circle"></i> 响应参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>code</td>
                            <td>Integer</td>
                            <td>状态码，1表示成功</td>
                            <td>1</td>
                        </tr>
                        <tr>
                            <td>msg</td>
                            <td>String</td>
                            <td>返回信息</td>
                            <td>支付创建成功</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object</td>
                            <td>返回数据</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>data.order_no</td>
                            <td>String</td>
                            <td>系统订单号</td>
                            <td>P20250820233733457d605ef</td>
                        </tr>
                        <tr>
                            <td>data.trade_no</td>
                            <td>String</td>
                            <td>商户订单号</td>
                            <td>TEST1755704243488</td>
                        </tr>
                        <tr>
                            <td>data.amount</td>
                            <td>Integer</td>
                            <td>订单金额</td>
                            <td>100</td>
                        </tr>
                        <tr>
                            <td>data.pay_data_type</td>
                            <td>Integer</td>
                            <td>支付数据类型</td>
                            <td>0</td>
                        </tr>
                        <tr>
                            <td>data.pay_url</td>
                            <td>String</td>
                            <td>支付链接</td>
                            <td>http://localhost:3002/payment/lNKN6IRm...</td>
                        </tr>
                        <tr>
                            <td>data.qrcode_url</td>
                            <td>String</td>
                            <td>二维码链接</td>
                            <td>""</td>
                        </tr>
                        <tr>
                            <td>time</td>
                            <td>Integer</td>
                            <td>响应时间戳</td>
                            <td>1755704254</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-reply"></i> 响应示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>{
    "code": 1,
    "msg": "支付创建成功",
    "data": {
        "order_no": "P20250820233733457d605ef",
        "trade_no": "TEST1755704243488",
        "amount": 100,
        "pay_data_type": 0,
        "pay_url": "http://localhost:3002/payment/lNKN6IRmOeuZPUYg-JPnV__4FTvxgiYbnrH-kemtWRfNJOGthj3f4iUcpgqXfbVfe8ERRquRKO9I32lRaiffpqZa5fSF51bx_vhOpJ-DNwKH7OzGAdEAbMTYvqvGTGfSSmdQeueiR0CL8GcU5ha52jHuCUXx2K70IYC1NU6CCLp14Sf67BBUjZBdzgJtnwpvpIdhcML8NKs0W8s5AdNB8Q",
        "qrcode_url": ""
    },
    "time": 1755704254
}</code>
                </div>
            </div>
                </div>

                    <div id="transfer" class="content-section">
                        <div class="section">
                            <h2><i class="fas fa-arrow-up"></i> 代付接口</h2>

                            <div class="api-endpoint">
                                <h3><span class="method post">POST</span> /repay/unifiedOrder</h3>
                                <p>创建代付订单，从商户账户向指定账户转账</p>
                            </div>

                            <div class="alert alert-warning">
                                <strong>重要提醒：</strong> 代付接口会直接从您的账户扣款，请确保接收方信息准确无误。
                            </div>

                            <h3><i class="fas fa-list"></i> 请求参数</h3>
                            <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>merchant_id</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户号</td>
                            <td>10001</td>
                        </tr>
                        <tr>
                            <td>amount</td>
                            <td>Decimal</td>
                            <td><span class="required">是</span></td>
                            <td>代付金额，保留2位小数</td>
                            <td>100.00</td>
                        </tr>
                        <tr>
                            <td>currency</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>货币类型</td>
                            <td>BRL</td>
                        </tr>
                        <tr>
                            <td>channel_code</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>代付通道编码</td>
                            <td>pix_transfer</td>
                        </tr>
                        <tr>
                            <td>merchant_order_no</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户订单号，唯一</td>
                            <td>TRANSFER20240101001</td>
                        </tr>
                        <tr>
                            <td>notify_url</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>异步通知地址</td>
                            <td>https://your-site.com/transfer-notify</td>
                        </tr>
                        <tr>
                            <td>subject</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>代付说明</td>
                            <td>佣金发放</td>
                        </tr>
                        <tr>
                            <td>extra_params</td>
                            <td>Object</td>
                            <td><span class="required">是</span></td>
                            <td>收款人信息</td>
                            <td>见下表</td>
                        </tr>
                        <tr>
                            <td>timestamp</td>
                            <td>Integer</td>
                            <td><span class="required">是</span></td>
                            <td>时间戳</td>
                            <td>1640995200</td>
                        </tr>
                        <tr>
                            <td>sign_type</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名类型</td>
                            <td>md5</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名</td>
                            <td>A1B2C3D4...</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-user"></i> extra_params 收款人信息</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>name</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>收款人姓名</td>
                            <td>João Silva</td>
                        </tr>
                        <tr>
                            <td>CPF</td>
                            <td>String</td>
                            <td><span class="optional">否</span></td>
                            <td>巴西个人税号</td>
                            <td>12345678901</td>
                        </tr>
                        <tr>
                            <td>PHONE</td>
                            <td>String</td>
                            <td><span class="optional">否</span></td>
                            <td>手机号码</td>
                            <td>+5511999999999</td>
                        </tr>
                        <tr>
                            <td>EMAIL</td>
                            <td>String</td>
                            <td><span class="optional">否</span></td>
                            <td>邮箱地址</td>
                            <td><EMAIL></td>
                        </tr>
                        <tr>
                            <td>EVP</td>
                            <td>String</td>
                            <td><span class="optional">否</span></td>
                            <td>PIX随机密钥</td>
                            <td>abc123-def456-ghi789</td>
                        </tr>
                    </tbody>
                </table>

                <div class="alert alert-info">
                    <strong>收款人信息说明：</strong> CPF、PHONE、EMAIL、EVP 四个字段至少填写一个，系统会按优先级自动选择支付方式。
                </div>

                <h3><i class="fas fa-code"></i> 请求示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>POST /repay/unifiedOrder HTTP/1.1
Host: api.yourpay.com
Content-Type: application/json

{
    "merchant_id": "10001",
    "amount": "100.00",
    "currency": "BRL",
    "channel_code": "pix_transfer",
    "merchant_order_no": "TRANSFER20240101001",
    "notify_url": "https://your-site.com/transfer-notify",
    "subject": "佣金发放",
    "extra_params": {
        "name": "João Silva",
        "CPF": "12345678901",
        "PHONE": "+5511999999999",
        "EMAIL": "<EMAIL>"
    },
    "timestamp": 1640995200,
    "sign_type": "md5",
    "sign": "B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7"
}</code>
                </div>

                <h3><i class="fas fa-check-circle"></i> 响应参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>code</td>
                            <td>Integer</td>
                            <td>状态码，0表示成功</td>
                            <td>0</td>
                        </tr>
                        <tr>
                            <td>msg</td>
                            <td>String</td>
                            <td>返回信息</td>
                            <td>success</td>
                        </tr>
                        <tr>
                            <td>data</td>
                            <td>Object</td>
                            <td>返回数据</td>
                            <td>-</td>
                        </tr>
                        <tr>
                            <td>data.order_no</td>
                            <td>String</td>
                            <td>系统订单号</td>
                            <td>SYS20240101002</td>
                        </tr>
                        <tr>
                            <td>data.status</td>
                            <td>String</td>
                            <td>订单状态</td>
                            <td>1</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-reply"></i> 响应示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>{
    "code": 0,
    "msg": "success",
    "data": {
        "order_no": "SYS20240101002",
        "status": "1"
    }
}</code>
                </div>
            </div>
                </div>

                <div id="order-query" class="content-section">
            <div class="section">
                <h2><i class="fas fa-search"></i> 订单查询</h2>

                <div class="api-endpoint">
                    <h3><span class="method post">POST</span> /pay/query</h3>
                    <p>查询代收订单状态</p>
                </div>

                <h3><i class="fas fa-list"></i> 请求参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>merchant_id</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户号</td>
                            <td>10001</td>
                        </tr>
                        <tr>
                            <td>merchant_order_no</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户订单号</td>
                            <td>ORDER20240101001</td>
                        </tr>
                        <tr>
                            <td>timestamp</td>
                            <td>Integer</td>
                            <td><span class="required">是</span></td>
                            <td>时间戳</td>
                            <td>1640995200</td>
                        </tr>
                        <tr>
                            <td>sign_type</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名类型</td>
                            <td>md5</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名</td>
                            <td>A1B2C3D4...</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-check-circle"></i> 响应参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>code</td>
                            <td>Integer</td>
                            <td>状态码，0表示成功</td>
                            <td>0</td>
                        </tr>
                        <tr>
                            <td>msg</td>
                            <td>String</td>
                            <td>返回信息</td>
                            <td>success</td>
                        </tr>
                        <tr>
                            <td>data.order_no</td>
                            <td>String</td>
                            <td>系统订单号</td>
                            <td>SYS20240101001</td>
                        </tr>
                        <tr>
                            <td>data.merchant_order_no</td>
                            <td>String</td>
                            <td>商户订单号</td>
                            <td>ORDER20240101001</td>
                        </tr>
                        <tr>
                            <td>data.amount</td>
                            <td>Decimal</td>
                            <td>订单金额</td>
                            <td>100.00</td>
                        </tr>
                        <tr>
                            <td>data.status</td>
                            <td>Integer</td>
                            <td>订单状态</td>
                            <td>2</td>
                        </tr>
                        <tr>
                            <td>data.create_time</td>
                            <td>Integer</td>
                            <td>创建时间</td>
                            <td>1640995200</td>
                        </tr>
                        <tr>
                            <td>data.success_time</td>
                            <td>Integer</td>
                            <td>成功时间</td>
                            <td>1640995260</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-info-circle"></i> 订单状态说明</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>状态值</th>
                            <th>状态名称（代收/代付）</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>0</td>
                            <td>待支付 / 待处理</td>
                            <td>订单已创建，等待用户支付</td>
                        </tr>
                        <tr>
                            <td>1</td>
                            <td>支付中 / 处理中</td>
                            <td>用户已发起支付，系统处理中</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>已支付</td>
                            <td>支付成功，资金已到账</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>已退款 / 已支付</td>
                            <td>订单已退款</td>
                        </tr>
                        <tr>
                            <td>-1</td>
                            <td>失败 / 失败</td>
                            <td>支付失败</td>
                        </tr>
                        <tr>
                            <td>-2</td>
                            <td>已过期</td>
                            <td>订单已过期</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-code"></i> 代收查询请求示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>POST /pay/query HTTP/1.1
Host: api.yourpay.com
Content-Type: application/json

{
    "merchant_id": "10001",
    "merchant_order_no": "ORDER20240101001",
    "timestamp": 1640995200,
    "sign_type": "md5",
    "sign": "C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8"
}</code>
                </div>

                <h3><i class="fas fa-reply"></i> 代收查询响应示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>{
    "code": 0,
    "msg": "success",
    "data": {
        "order_no": "SYS20240101001",
        "merchant_order_no": "ORDER20240101001",
        "amount": "100.00",
        "status": 2,
        "create_time": 1640995200,
        "success_time": 1640995260
    }
}</code>
                </div>

                <div class="api-endpoint">
                    <h3><span class="method post">POST</span> /repay/query</h3>
                    <p>查询代付订单状态</p>
                </div>

                <h3><i class="fas fa-list"></i> 代付查询请求参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>merchant_id</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户号</td>
                            <td>10001</td>
                        </tr>
                        <tr>
                            <td>merchant_order_no</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户订单号</td>
                            <td>TRANSFER20240101001</td>
                        </tr>
                        <tr>
                            <td>timestamp</td>
                            <td>Integer</td>
                            <td><span class="required">是</span></td>
                            <td>时间戳</td>
                            <td>1640995200</td>
                        </tr>
                        <tr>
                            <td>sign_type</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名类型</td>
                            <td>md5</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名</td>
                            <td>A1B2C3D4...</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-check-circle"></i> 代付查询响应参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>code</td>
                            <td>Integer</td>
                            <td>状态码，0表示成功</td>
                            <td>0</td>
                        </tr>
                        <tr>
                            <td>msg</td>
                            <td>String</td>
                            <td>返回信息</td>
                            <td>success</td>
                        </tr>
                        <tr>
                            <td>data.order_no</td>
                            <td>String</td>
                            <td>系统订单号</td>
                            <td>SYS20240101002</td>
                        </tr>
                        <tr>
                            <td>data.merchant_order_no</td>
                            <td>String</td>
                            <td>商户订单号</td>
                            <td>TRANSFER20240101001</td>
                        </tr>
                        <tr>
                            <td>data.amount</td>
                            <td>String</td>
                            <td>代付金额</td>
                            <td>100.00</td>
                        </tr>
                        <tr>
                            <td>data.status</td>
                            <td>Integer</td>
                            <td>订单状态</td>
                            <td>3</td>
                        </tr>
                        <tr>
                            <td>data.create_time</td>
                            <td>Integer</td>
                            <td>创建时间</td>
                            <td>1640995200</td>
                        </tr>
                        <tr>
                            <td>data.success_time</td>
                            <td>Integer</td>
                            <td>成功时间</td>
                            <td>1640995260</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-code"></i> 代付查询请求示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>POST /repay/query HTTP/1.1
Host: api.yourpay.com
Content-Type: application/json

{
    "merchant_id": "10001",
    "merchant_order_no": "TRANSFER20240101001",
    "timestamp": 1640995200,
    "sign_type": "md5",
    "sign": "D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9"
}</code>
                </div>

                <h3><i class="fas fa-reply"></i> 代付查询响应示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>{
    "code": 0,
    "msg": "success",
    "data": {
        "order_no": "SYS20240101002",
        "merchant_order_no": "TRANSFER20240101001",
        "amount": "100.00",
        "status": 2,
        "create_time": 1640995200,
        "success_time": 1640995260
    }
}</code>
                </div>
            </div>
                </div>

                <div id="balance-query" class="content-section">
            <div class="section">
                <h2><i class="fas fa-wallet"></i> 余额查询</h2>

                <div class="api-endpoint">
                    <h3><span class="method post">POST</span> /query/balance</h3>
                    <p>查询商户账户余额</p>
                </div>

                <h3><i class="fas fa-list"></i> 请求参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>merchant_id</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户号</td>
                            <td>10001</td>
                        </tr>
                        <tr>
                            <td>timestamp</td>
                            <td>Integer</td>
                            <td><span class="required">是</span></td>
                            <td>时间戳</td>
                            <td>1640995200</td>
                        </tr>
                        <tr>
                            <td>sign_type</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名类型</td>
                            <td>md5</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名</td>
                            <td>A1B2C3D4...</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-code"></i> 余额查询请求示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>POST /query/balance HTTP/1.1
Host: api.yourpay.com
Content-Type: application/json

{
    "merchant_id": "10001",
    "timestamp": 1640995200,
    "sign_type": "md5",
    "sign": "D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9"
}</code>
                </div>

                <h3><i class="fas fa-check-circle"></i> 余额查询响应参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>code</td>
                            <td>Integer</td>
                            <td>状态码，0表示成功</td>
                            <td>0</td>
                        </tr>
                        <tr>
                            <td>msg</td>
                            <td>String</td>
                            <td>返回信息</td>
                            <td>success</td>
                        </tr>
                        <tr>
                            <td>data.balance</td>
                            <td>Decimal</td>
                            <td>账户余额</td>
                            <td>9999.99</td>
                        </tr>
                        <tr>
                            <td>data.currency</td>
                            <td>String</td>
                            <td>货币类型</td>
                            <td>BRL</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-reply"></i> 余额查询响应示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>{
    "code": 0,
    "msg": "success",
    "data": {
        "balance": "9999.99",
        "currency": "BRL"
    }
}</code>
                </div>
            </div>
                </div>

                <div id="collection-callback" class="content-section">
            <div class="section">
                <h2><i class="fas fa-bell"></i> 代收回调通知</h2>

                <div class="alert alert-info">
                    <strong>重要说明：</strong> 当代收订单状态发生变化时，系统会向您提供的notify_url发送异步通知。
                </div>

                <h3><i class="fas fa-cogs"></i> 通知机制</h3>
                <ul style="margin: 20px 0; padding-left: 30px;">
                    <li><strong>通知方式：</strong>HTTP POST请求</li>
                    <li><strong>数据格式：</strong>JSON</li>
                    <li><strong>重试机制：</strong>失败后会重试，间隔时间递增</li>
                    <li><strong>超时时间：</strong>30秒</li>
                    <li><strong>成功标识：</strong>返回字符串"success"</li>
                </ul>

                <h3><i class="fas fa-list"></i> 通知参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>merchant_id</td>
                            <td>String</td>
                            <td>商户号</td>
                            <td>M2501082278</td>
                        </tr>
                        <tr>
                            <td>order_no</td>
                            <td>String</td>
                            <td>系统订单号</td>
                            <td>P202508190323213489c6b6b</td>
                        </tr>
                        <tr>
                            <td>merchant_order_no</td>
                            <td>String</td>
                            <td>商户订单号</td>
                            <td>TEST1755544994315</td>
                        </tr>
                        <tr>
                            <td>amount</td>
                            <td>String</td>
                            <td>订单金额</td>
                            <td>100.00</td>
                        </tr>
                        <tr>
                            <td>real_amount</td>
                            <td>String</td>
                            <td>实际到账金额</td>
                            <td>100.00</td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td>String</td>
                            <td>订单状态 成功=2，失败=-1</td>
                            <td>2</td>
                        </tr>
                        <tr>
                            <td>pay_time</td>
                            <td>String</td>
                            <td>支付完成时间</td>
                            <td>1755555448</td>
                        </tr>
                        <tr>
                            <td>timestamp</td>
                            <td>String</td>
                            <td>通知时间戳</td>
                            <td>1755555448</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>String</td>
                            <td>签名</td>
                            <td>B8536FFC8A20EF2848E6395FA4D142C3</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-code"></i> 通知示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>POST /your-notify-url HTTP/1.1
Host: your-site.com
Content-Type: application/json

{
    "merchant_id": "M2501082278",
    "order_no": "P202508190323213489c6b6b",
    "merchant_order_no": "TEST1755544994315",
    "amount": "100.00",
    "real_amount": "100.00",
    "status": "2",
    "pay_time": "1755555448",
    "timestamp": "1755555448",
    "sign": "B8536FFC8A20EF2848E6395FA4D142C3"
}</code>
                </div>

                <h3><i class="fas fa-shield-alt"></i> 签名验证</h3>
                <p>收到通知后，请务必验证签名确保数据安全：</p>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>// PHP示例
function verifySign($params, $secret) {
    $sign = $params['sign'];
    unset($params['sign']);

    ksort($params);
    $string = '';
    foreach ($params as $key => $value) {
        $string .= $key . '=' . $value . '&';
    }
    $string .= 'key=' . $secret;

    return strtoupper(md5($string)) === $sign;
}</code>
                </div>

                <h3><i class="fas fa-reply"></i> 响应要求</h3>
                <div class="alert alert-warning">
                    <strong>重要：</strong> 收到通知后，请返回字符串"success"表示处理成功，否则系统会认为通知失败并重试。
                </div>
            </div>
                </div>

                <div id="transfer-callback" class="content-section">
            <div class="section">
                <h2><i class="fas fa-bell-slash"></i> 代付回调通知</h2>

                <div class="alert alert-info">
                    <strong>重要说明：</strong> 当代付订单状态发生变化时，系统会向您提供的notify_url发送异步通知。
                </div>

                <h3><i class="fas fa-cogs"></i> 通知机制</h3>
                <ul style="margin: 20px 0; padding-left: 30px;">
                    <li><strong>通知方式：</strong>HTTP POST请求</li>
                    <li><strong>数据格式：</strong>JSON</li>
                    <li><strong>重试机制：</strong>失败后会重试，间隔时间递增</li>
                    <li><strong>超时时间：</strong>30秒</li>
                    <li><strong>成功标识：</strong>返回字符串"success"</li>
                </ul>

                <h3><i class="fas fa-list"></i> 通知参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>merchant_id</td>
                            <td>String</td>
                            <td>商户号</td>
                            <td>M2501082278</td>
                        </tr>
                        <tr>
                            <td>order_no</td>
                            <td>String</td>
                            <td>系统订单号</td>
                            <td>W202508150547031407128ce</td>
                        </tr>
                        <tr>
                            <td>merchant_order_no</td>
                            <td>String</td>
                            <td>商户订单号</td>
                            <td>TEST1755208021953</td>
                        </tr>
                        <tr>
                            <td>amount</td>
                            <td>String</td>
                            <td>订单金额</td>
                            <td>100</td>
                        </tr>
                        <tr>
                            <td>real_amount</td>
                            <td>String</td>
                            <td>实际到账金额</td>
                            <td>99.7</td>
                        </tr>
                        <tr>
                            <td>status</td>
                            <td>String</td>
                            <td>订单状态 成功=3，失败=-1</td>
                            <td>3</td>
                        </tr>
                        <tr>
                            <td>pay_time</td>
                            <td>String</td>
                            <td>支付完成时间</td>
                            <td>1755225829</td>
                        </tr>
                        <tr>
                            <td>timestamp</td>
                            <td>String</td>
                            <td>通知时间戳</td>
                            <td>1755225829</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>String</td>
                            <td>签名</td>
                            <td>A40F6622E8391B34E63E7962FD465315</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-code"></i> 通知示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>POST /your-transfer-notify-url HTTP/1.1
Host: your-site.com
Content-Type: application/json

{
    "merchant_id": "M2501082278",
    "order_no": "W202508150547031407128ce",
    "merchant_order_no": "TEST1755208021953",
    "amount": "100",
    "real_amount": "99.7",
    "status": "3",
    "pay_time": "1755225829",
    "timestamp": "1755225829",
    "sign": "A40F6622E8391B34E63E7962FD465315"
}</code>
                </div>

                <h3><i class="fas fa-shield-alt"></i> 签名验证</h3>
                <p>收到通知后，请务必验证签名确保数据安全：</p>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>// PHP示例
function verifySign($params, $secret) {
    $sign = $params['sign'];
    unset($params['sign']);

    ksort($params);
    $string = '';
    foreach ($params as $key => $value) {
        $string .= $key . '=' . $value . '&';
    }
    $string .= 'key=' . $secret;

    return strtoupper(md5($string)) === $sign;
}</code>
                </div>

                <h3><i class="fas fa-reply"></i> 响应要求</h3>
                <div class="alert alert-warning">
                    <strong>重要：</strong> 收到通知后，请返回字符串"success"表示处理成功，否则系统会认为通知失败并重试。
                </div>
            </div>
                </div>

                <div id="order-receipt" class="content-section">
            <div class="section">
                <h2><i class="fas fa-receipt"></i> 订单凭证</h2>

                <div class="alert alert-info">
                    <strong>功能说明：</strong> 提供订单凭证查询和下载功能，支持PDF格式的交易凭证。
                </div>

                <div class="api-endpoint">
                    <h3><span class="method post">POST</span> /receipt/query</h3>
                    <p>查询订单凭证信息</p>
                </div>

                <h3><i class="fas fa-list"></i> 请求参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必填</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>merchant_id</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>商户号</td>
                            <td>10001</td>
                        </tr>
                        <tr>
                            <td>order_no</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>系统订单号</td>
                            <td>SYS20240101001</td>
                        </tr>
                        <tr>
                            <td>timestamp</td>
                            <td>Integer</td>
                            <td><span class="required">是</span></td>
                            <td>时间戳</td>
                            <td>1640995200</td>
                        </tr>
                        <tr>
                            <td>sign</td>
                            <td>String</td>
                            <td><span class="required">是</span></td>
                            <td>签名</td>
                            <td>A1B2C3D4...</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-code"></i> 请求示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>POST /receipt/query HTTP/1.1
Host: api.yourpay.com
Content-Type: application/json

{
    "merchant_id": "10001",
    "order_no": "SYS20240101001",
    "timestamp": 1640995200,
    "sign": "E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0"
}</code>
                </div>

                <h3><i class="fas fa-check-circle"></i> 响应参数</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>说明</th>
                            <th>示例值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>code</td>
                            <td>Integer</td>
                            <td>状态码，0表示成功</td>
                            <td>0</td>
                        </tr>
                        <tr>
                            <td>msg</td>
                            <td>String</td>
                            <td>返回信息</td>
                            <td>success</td>
                        </tr>
                        <tr>
                            <td>data.receipt_url</td>
                            <td>String</td>
                            <td>凭证下载链接</td>
                            <td>https://api.yourpay.com/receipt/download/xxx</td>
                        </tr>
                        <tr>
                            <td>data.receipt_status</td>
                            <td>String</td>
                            <td>凭证状态</td>
                            <td>available</td>
                        </tr>
                        <tr>
                            <td>data.expire_time</td>
                            <td>Integer</td>
                            <td>链接过期时间</td>
                            <td>1641081600</td>
                        </tr>
                    </tbody>
                </table>

                <h3><i class="fas fa-reply"></i> 响应示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>{
    "code": 0,
    "msg": "success",
    "data": {
        "receipt_url": "https://api.yourpay.com/receipt/download/abc123def456",
        "receipt_status": "available",
        "expire_time": 1641081600
    }
}</code>
                </div>

                <h3><i class="fas fa-info-circle"></i> 凭证状态说明</h3>
                <table class="param-table">
                    <thead>
                        <tr>
                            <th>状态值</th>
                            <th>状态名称</th>
                            <th>说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>available</td>
                            <td>可用</td>
                            <td>凭证已生成，可以下载</td>
                        </tr>
                        <tr>
                            <td>generating</td>
                            <td>生成中</td>
                            <td>凭证正在生成，请稍后查询</td>
                        </tr>
                        <tr>
                            <td>unavailable</td>
                            <td>不可用</td>
                            <td>订单状态不支持生成凭证</td>
                        </tr>
                    </tbody>
                </table>

                <div class="alert alert-warning">
                    <strong>注意事项：</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li>只有已支付成功的订单才能生成凭证</li>
                        <li>凭证下载链接有效期为24小时</li>
                        <li>凭证为PDF格式，包含完整的交易信息</li>
                        <li>每个订单的凭证只能生成一次</li>
                    </ul>
                </div>
            </div>
                </div>

                <div id="examples" class="content-section">
            <div class="section">
                <h2><i class="fas fa-code"></i> 示例代码</h2>

                <h3><i class="fab fa-php"></i> PHP 示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>&lt;?php
class PaymentAPI {
    private $merchantId;
    private $secretKey;
    private $apiUrl;

    public function __construct($merchantId, $secretKey, $apiUrl) {
        $this->merchantId = $merchantId;
        $this->secretKey = $secretKey;
        $this->apiUrl = $apiUrl;
    }

    // 生成签名
    private function generateSign($params) {
        unset($params['sign']);
        ksort($params);

        $string = '';
        foreach ($params as $key => $value) {
            $string .= $key . '=' . $value . '&';
        }
        $string .= 'key=' . $this->secretKey;

        return strtoupper(md5($string));
    }

    // 代收下单
    public function createPayOrder($amount, $channelCode, $merchantOrderNo, $notifyUrl, $subject) {
        $params = [
            'merchant_id' => $this->merchantId,
            'amount' => number_format($amount, 2, '.', ''),
            'currency' => 'BRL',
            'channel_code' => $channelCode,
            'merchant_order_no' => $merchantOrderNo,
            'notify_url' => $notifyUrl,
            'subject' => $subject,
            'timestamp' => time()
        ];

        $params['sign'] = $this->generateSign($params);

        return $this->httpPost('/pay/unifiedOrder', $params);
    }

    // 代付下单
    public function createTransferOrder($amount, $channelCode, $merchantOrderNo, $notifyUrl, $subject, $extraParams) {
        $params = [
            'merchant_id' => $this->merchantId,
            'amount' => number_format($amount, 2, '.', ''),
            'currency' => 'BRL',
            'channel_code' => $channelCode,
            'merchant_order_no' => $merchantOrderNo,
            'notify_url' => $notifyUrl,
            'subject' => $subject,
            'extra_params' => $extraParams,
            'timestamp' => time()
        ];

        $params['sign'] = $this->generateSign($params);

        return $this->httpPost('/repay/unifiedOrder', $params);
    }

    // HTTP请求
    private function httpPost($uri, $data) {
        $url = $this->apiUrl . $uri;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true);
    }
}

// 使用示例
$api = new PaymentAPI('10001', 'your_secret_key', 'https://api.yourpay.com');

// 创建代收订单
$result = $api->createPayOrder(
    100.00,                    // 金额
    'pix_001',                 // 通道编码
    'ORDER' . time(),          // 商户订单号
    'https://your-site.com/notify', // 通知地址
    '商品购买'                  // 商品描述
);

// 创建代付订单
$transferResult = $api->createTransferOrder(
    50.00,                     // 金额
    'pix_transfer',            // 通道编码
    'TRANSFER' . time(),       // 商户订单号
    'https://your-site.com/transfer-notify', // 通知地址
    '佣金发放',                 // 说明
    [                          // 收款人信息
        'name' => 'João Silva',
        'CPF' => '12345678901'
    ]
);
?&gt;
                    </code>
                </div>

                <h3><i class="fab fa-js"></i> JavaScript 示例</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                    <code>
const crypto = require('crypto');
const axios = require('axios');

class PaymentAPI {
    constructor(merchantId, secretKey, apiUrl) {
        this.merchantId = merchantId;
        this.secretKey = secretKey;
        this.apiUrl = apiUrl;
    }

    // 生成签名
    generateSign(params) {
        delete params.sign;

        const sortedKeys = Object.keys(params).sort();
        let string = '';

        sortedKeys.forEach(key => {
            string += `${key}=${params[key]}&`;
        });
        string += `key=${this.secretKey}`;

        return crypto.createHash('md5').update(string).digest('hex').toUpperCase();
    }

    // 代收下单
    async createPayOrder(amount, channelCode, merchantOrderNo, notifyUrl, subject) {
        const params = {
            merchant_id: this.merchantId,
            amount: amount.toFixed(2),
            currency: 'BRL',
            channel_code: channelCode,
            merchant_order_no: merchantOrderNo,
            notify_url: notifyUrl,
            subject: subject,
            timestamp: Math.floor(Date.now() / 1000)
        };

        params.sign = this.generateSign({...params});

        try {
            const response = await axios.post(`${this.apiUrl}/pay/unifiedOrder`, params, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });

            return response.data;
        } catch (error) {
            throw new Error(`API请求失败: ${error.message}`);
        }
    }

    // 查询订单
    async queryOrder(merchantOrderNo) {
        const params = {
            merchant_id: this.merchantId,
            merchant_order_no: merchantOrderNo,
            timestamp: Math.floor(Date.now() / 1000)
        };

        params.sign = this.generateSign({...params});

        try {
            const response = await axios.post(`${this.apiUrl}/query/order`, params, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });

            return response.data;
        } catch (error) {
            throw new Error(`查询失败: ${error.message}`);
        }
    }
}

// 使用示例
const api = new PaymentAPI('10001', 'your_secret_key', 'https://api.yourpay.com');

// 创建订单
api.createPayOrder(
    100.00,
    'pix_001',
    `ORDER${Date.now()}`,
    'https://your-site.com/notify',
    '商品购买'
).then(result => {
    console.log('订单创建成功:', result);
}).catch(error => {
    console.error('订单创建失败:', error);
});
                    </code>
                </div>
            </div>
                </div>

                    <div id="auth" class="content-section">
                        <div class="section">
                            <h2><i class="fas fa-shield-alt"></i> 接口认证</h2>

                            <div class="alert alert-warning">
                                <strong>安全提醒：</strong> 请妥善保管您的商户密钥，不要在客户端代码中暴露密钥信息。
                            </div>

                            <h3><i class="fas fa-key"></i> 签名算法</h3>
                            <p>所有接口请求都需要进行签名验证，签名算法如下：</p>

                            <div class="code-block">
                                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                                <code>
1. 将所有请求参数（除sign外）按参数名ASCII码升序排序
2. 将排序后的参数按 key=value&key=value 格式拼接
3. 在拼接字符串末尾加上 &key=商户密钥
4. 对拼接后的字符串进行MD5加密，转为大写
                                </code>
                            </div>

                            <h3><i class="fas fa-code"></i> 签名示例</h3>
                            <div class="code-block">
                                <button class="copy-btn" onclick="copyToClipboard(this)">复制</button>
                                <code>
// 原始参数
{
  "merchant_id": "10001",
  "amount": "100.00",
  "currency": "BRL",
  "timestamp": "1640995200"
}

// 排序后拼接
amount=100.00&currency=BRL&merchant_id=10001&timestamp=1640995200&key=YOUR_SECRET_KEY

// MD5加密后
sign = MD5("amount=100.00&currency=BRL&merchant_id=10001&timestamp=1640995200&key=YOUR_SECRET_KEY").toUpperCase()
                                </code>
                            </div>

                            <h3><i class="fas fa-clock"></i> 时间戳验证</h3>
                            <p>为防止重放攻击，系统会验证请求时间戳，要求：</p>
                            <ul style="margin: 20px 0; padding-left: 30px;">
                                <li>timestamp 参数为10位Unix时间戳</li>
                                <li>请求时间与服务器时间差不超过5分钟</li>
                                <li>建议每次请求使用当前时间戳</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Footer 应该在 content-body 内部 -->
                    <div class="footer">
                        <p>&copy; 2024 PaymentAPI Documentation</p>
                        <p>
                            For technical support: <a href="mailto:<EMAIL>"><EMAIL></a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- 右侧目录索引 -->
            <div class="toc-sidebar" id="toc-sidebar">
                <div class="toc-title">目录导航</div>
                <ul class="toc-list" id="toc-list">
                    <!-- 目录将通过JavaScript动态生成 -->
                </ul>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        function showSection(sectionName) {
            // Hide all content sections
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });

            // Remove active class from all nav items
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.classList.remove('active');
            });

            // Show selected section
            document.getElementById(sectionName).classList.add('active');

            // Add active class to clicked nav item
            event.target.classList.add('active');

            // Update content title
            const titles = {
                'overview': '接口概述',
                'auth': '接口认证',
                'collection': '代收接口',
                'transfer': '代付接口',
                'order-query': '订单查询',
                'balance-query': '余额查询',
                'collection-callback': '代收回调',
                'transfer-callback': '代付回调',
                'order-receipt': '订单凭证',
                'examples': '示例代码'
            };
            document.getElementById('content-title').textContent = titles[sectionName] || '文档';

            // Close mobile menu if open
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('open');
            }

            // Generate table of contents and fix layout
            setTimeout(() => {
                console.log(`Switching to section: ${sectionName}`);
                generateTOC();

                // Fix indentation for the current section
                if (typeof fixIndentation === 'function') {
                    fixIndentation();
                }

                // Debug the current state
                if (typeof debugPageStructure === 'function') {
                    debugPageStructure();
                }
            }, 200); // 增加延迟确保DOM更新完成

            // Smooth scroll to top
            const contentBody = document.querySelector('.content-body');
            if (contentBody) {
                contentBody.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            } else {
                // 如果找不到 content-body，滚动整个窗口
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
        }

        function toggleMobileMenu() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // 生成目录
        function generateTOC() {
            const activeSection = document.querySelector('.content-section.active');
            const tocList = document.getElementById('toc-list');

            console.log('Generating TOC...', {
                activeSection: activeSection ? activeSection.id : 'none',
                tocList: !!tocList
            });

            if (!activeSection || !tocList) {
                console.warn('Cannot generate TOC: missing elements');
                return;
            }

            const headings = activeSection.querySelectorAll('h2, h3');
            console.log(`Found ${headings.length} headings`);
            tocList.innerHTML = '';

            headings.forEach((heading, index) => {
                const level = heading.tagName.toLowerCase();
                const text = heading.textContent.replace(/^\s*[\d\.\s]*/, ''); // 移除编号
                const id = `heading-${index}`;

                // 给标题添加ID，用于锚点跳转
                heading.id = id;

                const li = document.createElement('li');
                li.className = 'toc-item';

                const a = document.createElement('a');
                a.href = `#${id}`;
                a.className = `toc-link level-${level.charAt(1)}`;
                a.textContent = text;
                a.onclick = function(e) {
                    e.preventDefault();
                    console.log(`TOC clicked: ${id}`);

                    const targetElement = document.getElementById(id);
                    if (targetElement) {
                        console.log(`Target element found: ${targetElement.tagName}`);

                        // 获取目标元素的位置
                        const rect = targetElement.getBoundingClientRect();
                        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                        const targetPosition = rect.top + scrollTop - 100; // 留出100px的顶部空间

                        // 平滑滚动到目标位置
                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });

                        // 更新活跃状态
                        setTimeout(() => {
                            updateActiveTOC(id);
                        }, 100);
                    } else {
                        console.warn(`Target element not found: ${id}`);
                    }
                };

                li.appendChild(a);
                tocList.appendChild(li);
            });
        }

        // 更新目录活跃状态
        function updateActiveTOC(activeId) {
            const tocLinks = document.querySelectorAll('.toc-link');
            tocLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${activeId}`) {
                    link.classList.add('active');
                }
            });
        }

        // 滚动监听，自动更新目录活跃状态
        function setupScrollSpy() {
            const contentBody = document.querySelector('.content-body');
            if (!contentBody) {
                console.warn('Content body not found, scroll spy disabled');
                return;
            }

            contentBody.addEventListener('scroll', function() {
                const headings = document.querySelectorAll('.content-section.active h2, .content-section.active h3');
                let activeHeading = null;

                headings.forEach(heading => {
                    const rect = heading.getBoundingClientRect();
                    if (rect.top <= 100 && rect.top >= -100) {
                        activeHeading = heading;
                    }
                });

                if (activeHeading) {
                    updateActiveTOC(activeHeading.id);
                }
            });
        }

        function copyToClipboard(button) {
            const codeBlock = button.nextElementSibling;
            const text = codeBlock.textContent.trim();

            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopySuccess(button);
                }).catch(() => {
                    fallbackCopyTextToClipboard(text, button);
                });
            } else {
                fallbackCopyTextToClipboard(text, button);
            }
        }

        function fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopySuccess(button);
            } catch (err) {
                console.error('复制失败', err);
            }

            document.body.removeChild(textArea);
        }

        function showCopySuccess(button) {
            const originalText = button.textContent;
            button.textContent = '已复制!';
            button.style.background = '#28a745';

            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '#667eea';
            }, 2000);
        }

        // Add smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading animation with tech effect
        window.addEventListener('load', function() {
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.8s ease';

            // Create loading effect
            const loadingOverlay = document.createElement('div');
            loadingOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: var(--bg-primary);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                transition: opacity 0.5s ease;
            `;
            loadingOverlay.innerHTML = `
                <div style="text-align: center;">
                    <div style="width: 32px; height: 32px; border: 2px solid #e5e7eb; border-top: 2px solid var(--primary-color); border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
                    <div style="color: var(--text-secondary); font-size: 0.875rem;">Loading Documentation...</div>
                </div>
            `;

            // Add spin animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
            document.body.appendChild(loadingOverlay);

            setTimeout(() => {
                loadingOverlay.style.opacity = '0';
                document.body.style.opacity = '1';
                setTimeout(() => {
                    document.body.removeChild(loadingOverlay);
                }, 500);
            }, 1000);
        });

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        document.querySelector('[onclick="showSection(\'overview\')"]').click();
                        break;
                    case '2':
                        e.preventDefault();
                        document.querySelector('[onclick="showSection(\'auth\')"]').click();
                        break;
                    case '3':
                        e.preventDefault();
                        document.querySelector('[onclick="showSection(\'collection\')"]').click();
                        break;
                    case '4':
                        e.preventDefault();
                        document.querySelector('[onclick="showSection(\'transfer\')"]').click();
                        break;
                    case '5':
                        e.preventDefault();
                        document.querySelector('[onclick="showSection(\'query\')"]').click();
                        break;
                    case '6':
                        e.preventDefault();
                        document.querySelector('[onclick="showSection(\'callback\')"]').click();
                        break;
                    case '7':
                        e.preventDefault();
                        document.querySelector('[onclick="showSection(\'examples\')"]').click();
                        break;
                }
            }
        });

        // Add search functionality
        function addSearchBox() {
            const sidebar = document.querySelector('.nav-menu');
            if (!sidebar) {
                console.warn('Navigation menu not found, search box not added');
                return;
            }

            console.log('Adding search box to sidebar');
            const searchBox = document.createElement('div');
            searchBox.innerHTML = `
                <div style="padding: 0 20px 20px 20px; border-bottom: 1px solid var(--border-color); margin-bottom: 20px;">
                    <input type="text" id="searchInput" placeholder="搜索文档内容..."
                           style="width: 100%; padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 6px;
                                  font-size: 0.875rem; outline: none; background: var(--bg-primary); color: var(--text-primary);">
                    <div id="searchResults" style="margin-top: 8px; display: none; max-height: 200px; overflow-y: auto;"></div>
                </div>
            `;
            sidebar.insertBefore(searchBox, sidebar.firstChild);

            // 等待DOM更新后再获取元素
            setTimeout(() => {
                const searchInput = document.getElementById('searchInput');
                const searchResults = document.getElementById('searchResults');

                if (!searchInput || !searchResults) {
                    console.warn('Search elements not found after DOM update');
                    console.log('Available elements:', {
                        searchInput: !!searchInput,
                        searchResults: !!searchResults,
                        allInputs: document.querySelectorAll('input').length,
                        allDivs: document.querySelectorAll('#searchResults').length
                    });
                    return;
                }

                console.log('Search elements found, setting up events');
                setupSearchEvents(searchInput, searchResults);
            }, 100); // 增加延迟时间
        }

        // 设置搜索事件
        function setupSearchEvents(searchInput, searchResults) {
            searchInput.addEventListener('input', function() {
                const query = this.value.toLowerCase().trim();
                if (query.length < 2) {
                    searchResults.style.display = 'none';
                    return;
                }

                const allText = document.querySelectorAll('.content-section h2, .content-section h3, .content-section p, .content-section td');
                const results = [];

                try {
                    allText.forEach(element => {
                        if (element && element.textContent && element.textContent.toLowerCase().includes(query)) {
                            const section = element.closest('.content-section');
                            if (section && section.id) {
                                const sectionId = section.id;
                                const navItem = document.querySelector(`[onclick="showSection('${sectionId}')"]`);
                                const sectionName = navItem ? navItem.textContent.trim() : sectionId;
                                results.push({
                                    text: element.textContent.substring(0, 100) + '...',
                                    section: sectionName,
                                    sectionId: sectionId
                                });
                            }
                        }
                    });
                } catch (error) {
                    console.error('Error in search:', error);
                }

                if (results.length > 0) {
                    searchResults.innerHTML = results.slice(0, 5).map(result =>
                        `<div style="padding: 8px; background: #f8f9fa; margin: 2px 0; border-radius: 5px; cursor: pointer;"
                              onclick="showSection('${result.sectionId}')">
                            <strong>${result.section}</strong><br>
                            <small>${result.text}</small>
                         </div>`
                    ).join('');
                    searchResults.style.display = 'block';
                } else {
                    searchResults.innerHTML = '<div style="padding: 8px; color: #999;">未找到相关内容</div>';
                    searchResults.style.display = 'block';
                }
            });

            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                const searchContainer = searchInput.closest('div');
                if (searchContainer && !searchContainer.contains(e.target)) {
                    searchResults.style.display = 'none';
                }
            });
        }

        // 强力修复所有页面的布局问题
        function fixIndentation() {
            try {
                console.log('Applying powerful layout fix...');

                // 添加强力CSS修复
                const style = document.createElement('style');
                style.textContent = `
                    /* 强力修复所有布局问题 */
                    .content-section {
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    .content-section .section {
                        margin: 0 !important;
                        padding: 0 !important;
                    }

                    .content-section .section > * {
                        margin-left: 0 !important;
                        padding-left: 0 !important;
                    }

                    .content-section h2,
                    .content-section h3,
                    .content-section p,
                    .content-section div,
                    .content-section table,
                    .content-section .code-block,
                    .content-section .alert,
                    .content-section .api-endpoint,
                    .content-section ul,
                    .content-section ol {
                        margin-left: 0 !important;
                        padding-left: 0 !important;
                    }

                    /* 恢复必要的内边距 */
                    .content-section table th,
                    .content-section table td {
                        padding: 12px 16px !important;
                    }

                    .content-section .code-block {
                        padding: 20px !important;
                    }

                    .content-section .alert {
                        padding: 16px 20px !important;
                    }

                    .content-section .api-endpoint {
                        padding: 20px !important;
                    }

                    .content-section ul,
                    .content-section ol {
                        padding-left: 20px !important;
                    }
                `;
                document.head.appendChild(style);

                console.log('Powerful layout fix applied');
            } catch (error) {
                console.error('Error applying layout fix:', error);
            }
        }

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });

        // 调试页面结构
        function debugPageStructure() {
            console.log('=== Page Structure Debug ===');
            console.log('Main content:', !!document.querySelector('.main-content'));
            console.log('Content wrapper:', !!document.querySelector('.content-wrapper'));
            console.log('Content body:', !!document.querySelector('.content-body'));
            console.log('TOC sidebar:', !!document.querySelector('.toc-sidebar'));

            const sections = document.querySelectorAll('.content-section');
            console.log(`Found ${sections.length} content sections:`);
            sections.forEach(section => {
                console.log(`- ${section.id}: ${section.classList.contains('active') ? 'ACTIVE' : 'inactive'}`);
            });

            const activeSection = document.querySelector('.content-section.active');
            if (activeSection) {
                const headings = activeSection.querySelectorAll('h2, h3');
                console.log(`Active section has ${headings.length} headings`);
            }
        }

        // Initialize search box and TOC
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing components...');
            try {
                console.log('1. Adding search box...');
                addSearchBox();

                console.log('2. Generating TOC...');
                generateTOC();

                console.log('3. Setting up scroll spy...');
                setupScrollSpy();

                console.log('4. Fixing indentation...');
                fixIndentation(); // 修复缩进问题

                console.log('5. Debug page structure...');
                debugPageStructure();

                console.log('Initialization complete');
            } catch (error) {
                console.error('Error during initialization:', error);
            }
        });



        // Add hover effects for interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add glow effect to buttons
            const buttons = document.querySelectorAll('.nav-tab, .copy-btn');
            buttons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.boxShadow = '0 0 20px rgba(0, 212, 255, 0.5)';
                });
                button.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.boxShadow = '';
                    }
                });
            });

            // Add typing effect to code blocks
            const codeBlocks = document.querySelectorAll('.code-block code');
            codeBlocks.forEach(block => {
                const originalText = block.textContent;
                block.addEventListener('mouseenter', function() {
                    this.style.borderLeft = '3px solid var(--primary-color)';
                    this.style.paddingLeft = '20px';
                    this.style.transition = 'all 0.3s ease';
                });
                block.addEventListener('mouseleave', function() {
                    this.style.borderLeft = '';
                    this.style.paddingLeft = '';
                });
            });
        });
    </script>
</body>
</html>
