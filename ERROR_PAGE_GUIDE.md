# 🚨 错误页面使用指南

## 📋 功能概述

ErrorPage.vue 是一个功能完善的错误处理页面，支持多种错误类型、自动重定向、错误信息复制等功能，为用户提供友好的错误处理体验。

## ✅ 主要功能

### 1. **多种错误类型支持**
- 🔑 **token** - 支付链接无效或过期
- 🌐 **network** - 网络连接失败
- 🔧 **server** - 服务器错误
- 💳 **payment** - 支付处理失败
- ⏰ **expired** - 会话过期
- 🚫 **forbidden** - 访问被拒绝
- 🔍 **notfound** - 页面未找到
- ⚠️ **default** - 未知错误

### 2. **智能错误显示**
- 🎨 **动态图标** - 根据错误类型显示不同图标
- 📝 **详细信息** - 错误代码、时间戳等
- 🌍 **多语言支持** - 中英文错误信息
- 📱 **响应式设计** - 适配各种屏幕尺寸

### 3. **用户操作**
- 🏠 **返回首页** - 智能导航回上一页
- 🔄 **重新加载** - 刷新当前页面
- 📞 **联系客服** - 复制错误信息到剪贴板
- ⏱️ **自动重定向** - 某些错误类型自动刷新

## 🚀 使用方法

### **1. 路由跳转**
```javascript
// 基本用法
router.push('/error?type=token')

// 带自定义消息
router.push('/error?type=server&message=数据库连接失败')

// 带错误代码
router.push('/error?type=payment&code=PAY_001&message=支付网关超时')

// 带token参数（用于返回）
router.push('/error?type=network&token=abc123')
```

### **2. 编程式导航**
```javascript
// 在组件中使用
const handleError = (errorType, errorMessage = '', errorCode = '') => {
  const query = {
    type: errorType,
    ...(errorMessage && { message: errorMessage }),
    ...(errorCode && { code: errorCode }),
    ...(route.query.token && { token: route.query.token })
  }
  
  router.push({ path: '/error', query })
}

// 使用示例
handleError('network', '网络连接超时', 'NET_001')
```

### **3. API错误处理**
```javascript
// axios拦截器中使用
axios.interceptors.response.use(
  response => response,
  error => {
    const status = error.response?.status
    
    switch (status) {
      case 401:
        router.push('/error?type=expired')
        break
      case 403:
        router.push('/error?type=forbidden')
        break
      case 404:
        router.push('/error?type=notfound')
        break
      case 500:
        router.push('/error?type=server')
        break
      default:
        router.push('/error?type=network')
    }
    
    return Promise.reject(error)
  }
)
```

## 📊 错误类型详解

### **🔑 Token错误 (type=token)**
```javascript
// 使用场景
- 支付链接过期
- Token格式错误
- Token验证失败

// 特点
- 不自动重定向
- 显示联系客服按钮
- 错误代码: ERR_TOKEN_001
```

### **🌐 网络错误 (type=network)**
```javascript
// 使用场景
- 网络连接超时
- DNS解析失败
- 服务器无响应

// 特点
- 10秒后自动刷新
- 显示进度条
- 错误代码: ERR_NETWORK_001
```

### **🔧 服务器错误 (type=server)**
```javascript
// 使用场景
- 500内部服务器错误
- 数据库连接失败
- 服务不可用

// 特点
- 不自动重定向
- 显示联系客服按钮
- 错误代码: ERR_SERVER_001
```

### **💳 支付错误 (type=payment)**
```javascript
// 使用场景
- 支付网关错误
- 支付参数错误
- 支付处理失败

// 特点
- 不自动重定向
- 显示联系客服按钮
- 错误代码: ERR_PAYMENT_001
```

### **⏰ 会话过期 (type=expired)**
```javascript
// 使用场景
- 用户会话超时
- 登录状态失效
- 安全令牌过期

// 特点
- 10秒后自动刷新
- 显示安全提示
- 错误代码: ERR_SESSION_001
```

### **🚫 访问禁止 (type=forbidden)**
```javascript
// 使用场景
- 权限不足
- IP被限制
- 访问被拒绝

// 特点
- 不自动重定向
- 显示联系客服按钮
- 错误代码: ERR_ACCESS_001
```

### **🔍 页面未找到 (type=notfound)**
```javascript
// 使用场景
- 404页面不存在
- 路由配置错误
- 链接地址错误

// 特点
- 10秒后自动刷新
- 不显示错误详情
- 错误代码: ERR_404_001
```

## 🎨 界面特性

### **视觉设计**
- 🌈 **渐变背景** - 现代化视觉效果
- 🎭 **动画效果** - 图标弹跳动画
- 🎨 **彩色顶栏** - 彩虹色装饰条
- 🌙 **深色模式** - 自动适配系统主题

### **交互体验**
- 📱 **响应式布局** - 完美适配移动端
- ⌨️ **键盘导航** - 支持Tab键导航
- 🖱️ **鼠标悬停** - 按钮悬停效果
- 📋 **一键复制** - 错误信息快速复制

### **无障碍支持**
- 🔍 **屏幕阅读器** - 语义化HTML结构
- ⌨️ **键盘操作** - 完整键盘支持
- 🎨 **高对比度** - 清晰的颜色对比
- 📖 **多语言** - 国际化支持

## 🔧 自定义配置

### **修改错误类型**
```javascript
// 在ErrorPage.vue中添加新的错误类型
const errorTypes = {
  // 添加新的错误类型
  maintenance: {
    icon: '🔧',
    title: t('error.maintenance'),
    message: t('error.maintenanceMessage'),
    code: 'ERR_MAINTENANCE_001',
    details: true,
    autoRedirect: true
  }
}
```

### **自定义样式**
```css
/* 修改主题色 */
.error-page {
  background: linear-gradient(135deg, #your-color1, #your-color2);
}

/* 修改容器样式 */
.error-container {
  background: your-background-color;
  border-radius: your-border-radius;
}
```

### **添加新语言**
```javascript
// 在语言包中添加新的错误信息
{
  "error": {
    "maintenance": "系统维护中",
    "maintenanceMessage": "系统正在维护，预计30分钟后恢复正常"
  }
}
```

## 📱 移动端优化

### **响应式断点**
- **768px以下**: 平板适配
- **480px以下**: 手机适配
- **320px以下**: 小屏手机适配

### **移动端特性**
- 📱 **触摸友好** - 大按钮设计
- 🔄 **下拉刷新** - 支持手势刷新
- 📋 **长按复制** - 移动端复制优化
- 🎯 **点击区域** - 足够大的点击区域

## 🧪 测试用例

### **功能测试**
```javascript
// 测试不同错误类型
const testCases = [
  { type: 'token', expected: '支付链接无效' },
  { type: 'network', expected: '网络连接失败' },
  { type: 'server', expected: '服务器错误' },
  { type: 'payment', expected: '支付处理失败' }
]

testCases.forEach(test => {
  // 访问 /error?type=${test.type}
  // 验证页面显示是否正确
})
```

### **自动化测试**
```javascript
// E2E测试示例
describe('ErrorPage', () => {
  it('should display correct error message for token error', () => {
    cy.visit('/error?type=token')
    cy.contains('支付链接无效')
    cy.get('[data-testid="contact-support"]').should('be.visible')
  })
  
  it('should auto redirect for network error', () => {
    cy.visit('/error?type=network')
    cy.get('[data-testid="countdown"]').should('be.visible')
    // 等待10秒验证自动刷新
  })
})
```

## 🎯 最佳实践

### **1. 错误分类**
- **用户错误**: token、expired、forbidden
- **系统错误**: server、network、payment
- **页面错误**: notfound、default

### **2. 用户体验**
- 提供清晰的错误说明
- 给出具体的解决建议
- 保持友好的语言风格
- 提供多种操作选择

### **3. 技术实现**
- 统一的错误处理机制
- 完整的错误日志记录
- 合理的自动重试策略
- 优雅的降级方案

现在您的错误页面功能完善，用户体验友好！🚨✨
